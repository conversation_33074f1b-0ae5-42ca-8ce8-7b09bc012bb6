import {
	type MigrationInterface,
	type QueryRunner,
	Table,
	TableIndex,
} from 'typeorm';

export class AddSysConfigsTable1755236351466 implements MigrationInterface {
	public async up(queryRunner: QueryRunner): Promise<void> {
		// Create new table with key as primary key
		await queryRunner.createTable(
			new Table({
				name: 'sys_config',
				columns: [
					{
						name: 'key',
						type: 'varchar',
						length: '100',
						isPrimary: true,
						comment: 'Configuration key (primary key)',
					},
					{
						name: 'value',
						type: 'text',
						isNullable: true,
						comment: 'Configuration value',
					},
					{
						name: 'description',
						type: 'text',
						isNullable: true,
						comment: 'Description of what this config does',
					},
					{
						name: 'data_type',
						type: 'varchar',
						length: '50',
						default: '\'string\'',
						comment: 'Data type: string, number, boolean, json',
					},
					{
						name: 'category',
						type: 'varchar',
						length: '100',
						isNullable: true,
						comment: 'Category to group related configs',
					},
					{
						name: 'created_at',
						type: 'timestamp',
						default: 'CURRENT_TIMESTAMP',
						comment: 'Creation timestamp',
					},
					{
						name: 'updated_at',
						type: 'timestamp',
						default: 'CURRENT_TIMESTAMP',
						comment: 'Last update timestamp',
					},
				],
			}),
			true,
		);

		// Create indexes for performance
		await queryRunner.createIndex(
			'sys_config',
			new TableIndex({
				name: 'IDX_SYS_CONFIG_CATEGORY',
				columnNames: ['category'],
			}),
		);

		await queryRunner.createIndex(
			'sys_config',
			new TableIndex({
				name: 'IDX_SYS_CONFIG_DATA_TYPE',
				columnNames: ['data_type'],
			}),
		);

		// Insert comprehensive default configurations
		await queryRunner.query(`
			INSERT INTO sys_config (key, value, description, data_type, category) VALUES
			-- System Configuration
			('site_name', 'FS Player Service', 'Website name displayed in the header', 'string', 'system'),
			('site_description', 'Full Stack Player Service API', 'Website description for SEO', 'string', 'system'),
			('maintenance_mode', 'false', 'Enable/disable maintenance mode', 'boolean', 'system'),
			('ip_whitelist', '[]', 'Enable/disable maintenance mode', 'boolean', 'system'),
			('api_version', '1.0.0', 'Current API version', 'string', 'system'),

			-- Security Configuration
			('max_login_attempts', '5', 'Maximum login attempts before lockout', 'number', 'security'),
			('max_ip_login_attempts', '20', 'Maximum login attempts per IP', 'number', 'security'),
			('login_attempt_ttl', '900', 'Login attempt rate limit window in seconds (15 minutes)', 'number', 'security'),
			('session_timeout', '3600', 'Session timeout in seconds', 'number', 'security'),
			('api_rate_limit', '100', 'API rate limit per minute', 'number', 'security'),
			('password_min_length', '8', 'Minimum password length', 'number', 'security'),
			('password_require_special', 'true', 'Require special characters in password', 'boolean', 'security'),
			('jwt_expiration_time', '3600', 'JWT token expiration time in seconds', 'number', 'security'),
			('jwt_refresh_expiration_time', '604800', 'JWT refresh token expiration time in seconds (7 days)', 'number', 'security'),

			-- OTP Configuration
			('otp_ttl', '5', 'OTP expiration time in minutes', 'number', 'otp'),
			('otp_limit_ttl', '5', 'OTP rate limit window in minutes', 'number', 'otp'),
			('otp_max_limit_user', '3', 'Maximum OTP requests per user per window', 'number', 'otp'),
			('otp_max_limit_ip', '10', 'Maximum OTP requests per IP per window', 'number', 'otp'),
			('otp_length', '6', 'OTP code length', 'number', 'otp'),
			('otp_max_attempts', '3', 'Maximum OTP verification attempts', 'number', 'otp'),

			-- Cache Configuration
			('cache_ttl', '3600', 'Default cache TTL in seconds', 'number', 'performance'),
			('redis_ttl', '3600', 'Redis cache TTL in seconds', 'number', 'performance'),
			('sys_config_cache_ttl', '3600', 'System config cache TTL in seconds', 'number', 'performance'),
			('user_cache_ttl', '300', 'User token cache TTL in seconds (5 minutes)', 'number', 'performance'),

			-- Throttler Configuration
			('throttler_ttl', '60', 'Throttler window in seconds', 'number', 'throttling'),
			('throttler_limit', '10', 'Throttler request limit per window', 'number', 'throttling'),

			-- Email Configuration
			('mailer_api_host', 'http://localhost:3003', 'Mailer service API host', 'string', 'email'),
			('mailer_retry_attempts', '3', 'Email sending retry attempts', 'number', 'email'),

			-- Zalo Integration
			('ZALO_OA_URL', 'https://oauth.zaloapp.com/v4/oa/permission', 'Zalo OAuth authorization URL', 'string', 'zalo'),
			('ZALO_TOKEN_URL', 'https://oauth.zaloapp.com/v4/oa/access_token', 'Zalo OAuth token exchange URL', 'string', 'zalo'),
			('zalo_callback_timeout', '30', 'Zalo OAuth callback timeout in seconds', 'number', 'zalo'),

			-- Database Configuration
			('db_connection_timeout', '30000', 'Database connection timeout in milliseconds', 'number', 'database'),
			('db_query_timeout', '10000', 'Database query timeout in milliseconds', 'number', 'database'),

			-- File Upload Configuration
			('max_file_size', '10485760', 'Maximum file upload size in bytes (10MB)', 'number', 'upload'),
			('allowed_file_types', '["jpg","jpeg","png","gif","pdf","doc","docx"]', 'Allowed file upload types', 'json', 'upload'),

			-- Monitoring Configuration
			('enable_metrics', 'true', 'Enable application metrics collection', 'boolean', 'monitoring'),
			('metrics_interval', '60', 'Metrics collection interval in seconds', 'number', 'monitoring'),
			('log_level', 'info', 'Application log level', 'string', 'monitoring'),

			-- Feature Flags
			('enable_registration', 'true', 'Enable user registration', 'boolean', 'features'),
			('enable_social_login', 'true', 'Enable social media login', 'boolean', 'features'),
			('enable_otp_verification', 'true', 'Enable OTP verification', 'boolean', 'features'),
			('enable_email_notifications', 'true', 'Enable email notifications', 'boolean', 'features')
		`);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		// Drop indexes first
		await queryRunner.dropIndex('sys_config', 'IDX_SYS_CONFIG_CATEGORY');
		await queryRunner.dropIndex('sys_config', 'IDX_SYS_CONFIG_DATA_TYPE');

		// Drop table
		await queryRunner.dropTable('sys_config');
	}
}
