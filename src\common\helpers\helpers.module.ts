import { SysConfigModule } from '@modules/sys-config/sys-config.module';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { SysConfigEntity } from '../../modules/sys-config/entities/sys-config.entity';
import { SysConfigService } from '../../modules/sys-config/sys-config.service';
import { <PERSON><PERSON><PERSON>elper } from './cache.helper';
import { ConfigHelper } from './config.helper';

/**
 * Common helpers module
 * Provides shared utilities across the application
 */
@Module({
	imports: [
		SysConfigModule,
		TypeOrmModule.forFeature([SysConfigEntity]),
	],
	providers: [ConfigHelper, CacheHelper, SysConfigService],
	exports: [ConfigHelper, CacheHelper, SysConfigService],
})
export class HelpersModule {}
