import { InjectQueue } from '@nestjs/bull';
import { Injectable } from '@nestjs/common';
import type { Queue } from 'bull';

export interface PaymentQueueJob {
	paymentId: string;
	packKey: string;
	action: 'PROCESS_PAYMENT' | 'RETRY_PAYMENT' | 'NOTIFY_PAYMENT';
}

@Injectable()
export class PaymentQueueService {
	constructor(
		@InjectQueue('payment-queue') private readonly paymentQueue: Queue,
	) {}

	async addPaymentJob(data: PaymentQueueJob, delay?: number): Promise<void> {
		await this.paymentQueue.add('process-payment', data, {
			delay: delay || 0,
			attempts: 3,
			backoff: {
				type: 'exponential',
				delay: 2000,
			},
		});
	}

	async addRetryJob(paymentId: string, packKey: string): Promise<void> {
		await this.addPaymentJob({
			paymentId,
			packKey,
			action: 'RETRY_PAYMENT',
		});
	}

	async addNotificationJob(
		paymentId: string,
		packKey: string,
	): Promise<void> {
		await this.addPaymentJob({
			paymentId,
			packKey,
			action: 'NOTIFY_PAYMENT',
		});
	}
}
