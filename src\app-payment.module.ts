import { AuthPaymentModule } from '@modules/auth/auth-payment.module';
import { PaymentModule } from '@modules/payment/payment.module';
import { CacheModule } from '@nestjs/cache-manager';
import type { MiddlewareConsumer, NestModule } from '@nestjs/common';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { ScheduleModule } from '@nestjs/schedule';
import { ThrottlerModule } from '@nestjs/throttler';
import { TypeOrmModule } from '@nestjs/typeorm';
import { redisStore } from 'cache-manager-redis-store';
import { ResponseInterceptor } from 'interceptors/response-interceptor.service';
import { IpWhitelistMiddleware } from 'middleware/ip-whitelist.middleware';

import { JwtBlacklistMiddleware } from './middleware/jwt-blacklist.middleware';
import { HealthCheckerModule } from './modules/health-checker/health-checker.module';
import { UserModule } from './modules/user/user.module';
import { QueueModule } from './queue/queue.module';
import { ScheduleModule as CustomScheduleModule } from './schedule/schedule.module';
import { ApiConfigService } from './shared/services/api-config.service';
import { SharedModule } from './shared/shared.module';
import { ClsModule } from 'nestjs-cls';

@Module({
	imports: [
		CacheModule.register({
			isGlobal: true,
			useFactory: async(configService: ApiConfigService) => {
				return {
					store: redisStore,
					host: configService.redisConfig.socket.host,
					port: configService.redisConfig.socket.port,
					ttl: configService.redisConfig.ttl,
				};
			},
			inject: [ApiConfigService],
		}),
		AuthPaymentModule,
		ScheduleModule.forRoot(), // Kích hoạt scheduler
		UserModule.forRoot({ isPaymentService: true }),
		PaymentModule,
		ClsModule.forRoot({
			global: true,
			middleware: {
				mount: true,
			},
		}),
		ThrottlerModule.forRootAsync({
			imports: [SharedModule],
			useFactory: (configService: ApiConfigService) => ({
				throttlers: [configService.throttlerConfigs],
			}),
			inject: [ApiConfigService],
		}),
		ConfigModule.forRoot({
			isGlobal: true,
			envFilePath: '.env',
		}),
		TypeOrmModule.forRootAsync({
			imports: [SharedModule],
			useFactory: (configService: ApiConfigService) =>
				configService.postgresConfig,
			inject: [ApiConfigService],
		}),
		HealthCheckerModule,
		QueueModule,
		CustomScheduleModule,
	],
	providers: [
		IpWhitelistMiddleware,
		JwtBlacklistMiddleware,
		{
			provide: APP_INTERCEPTOR,
			useClass: ResponseInterceptor,
		},
	],
})
export class AppPaymentModule implements NestModule {
	configure(consumer: MiddlewareConsumer) {
		consumer.apply(IpWhitelistMiddleware).forRoutes('*');
		consumer
			.apply(JwtBlacklistMiddleware)
			.exclude(
				'/health',
			)
			.forRoutes('*');
	}
}
