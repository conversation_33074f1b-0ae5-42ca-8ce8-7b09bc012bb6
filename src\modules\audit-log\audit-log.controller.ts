// src/modules/audit-log/controllers/audit-log.controller.ts
import { AuditLogCategory } from '@constants/audit-log';
import { RoleType } from '@constants/role-type';
import { ApiCommonResponse } from '@decorators/api-common-response.decorator';
import { AuthUser } from '@decorators/auth-user.decorator';
import { Auth } from '@decorators/http.decorators';
import { Controller, DefaultValuePipe, Get, HttpCode, HttpStatus, ParseIntPipe, Query } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { UnauthorizedException } from 'exceptions';

import { UserAccountEntity } from '../user/user-account.entity';
import { AuditLogService } from './audit-log.service';
import { QueryAuditLogDto } from './dtos/audit-log.dto';
import { AuditLogSummaryDto, PaginatedAuditLogDto, RecentAuditLogDto } from './dtos/audit-log-response.dto';

@Controller('audit-log')
@ApiTags('Audit Log')
export class AuditLogController {
	constructor(
		private auditLogService: AuditLogService,
	) {}

	@Get('my-activity')
	@HttpCode(HttpStatus.OK)
	@Auth([RoleType.USER])
	@ApiCommonResponse({
		type: PaginatedAuditLogDto,
		description: 'Get my activity logs successfully.',
	})
	async getMyActivity(
		@AuthUser() user: UserAccountEntity,
		@Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
		@Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit: number,
		@Query('category') category?: string,
	) {
		// Kiểm tra xem user có tồn tại không
		if (!user || !user.userId) {
			throw new UnauthorizedException('User not authenticated');
		}
		
		// Xây dựng đối tượng query với các tham số đã được xử lý
		const query: QueryAuditLogDto = {
			userId: user.userId,
			action: category as AuditLogCategory === 'authentication'
				? 'login_success' : 'user_profile_updated',
			page,
			limit,
			// Chuyển logic kiểm tra category sang service layer
			category: category as AuditLogCategory | undefined,
		};
		
		return this.auditLogService.queryLogs(query);
	}

	@Get('my-summary')
	@HttpCode(HttpStatus.OK)
	@Auth([RoleType.USER])
	@ApiCommonResponse({
		type: [AuditLogSummaryDto],
		description: 'Get my activity summary successfully.',
	})
	async getMySummary(
		@AuthUser() user: UserAccountEntity,
		@Query('days', new DefaultValuePipe(7), ParseIntPipe) days: number,
	) {
		// Kiểm tra xem user có tồn tại không
		if (!user || !user.userId) {
			throw new UnauthorizedException('User not authenticated');
		}
		
		return this.auditLogService.getUserActivitySummary(user.userId, days);
	}

	@Get('cache-stats')
	@Auth([RoleType.ADMIN])
	@HttpCode(HttpStatus.OK)
	@ApiOperation({
		summary: 'Get cache statistics (Admin Only)',
		description:
		'Get detailed cache audit log. Requires admin authentication.',
	})
	@ApiCommonResponse({
		type: PaginatedAuditLogDto,
		description: 'Get audit logs successfully.',
	})
	async queryLogs(@Query() query: QueryAuditLogDto) {
		return this.auditLogService.queryLogs(query);
	}

	@Get('recent')
	@Auth([RoleType.ADMIN])
	@HttpCode(HttpStatus.OK)
	@ApiOperation({
		summary: 'Get recent logs (Admin Only)',
		description:
		'Get detailed recent audit log. Requires admin authentication.',
	})
	@ApiCommonResponse({
		type: [RecentAuditLogDto],
		description: 'Get recent audit logs successfully.',
	})
	async getRecentLogs(
		@Query('limit', new DefaultValuePipe(50), ParseIntPipe) limit: number,
	) {
		return this.auditLogService.getRecentLogs(undefined, limit);
	}
}