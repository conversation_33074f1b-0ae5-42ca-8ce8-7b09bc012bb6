import { Platform } from '@constants/platform';
import { Injectable } from '@nestjs/common';
import type { Request } from 'express';

@Injectable()
export class PlatformService {
	/**
	 * Detect platform from request and optional body platform
	 * @param req Express request object
	 @param bodyPlatform Optional platform from request body
	 * @returns Detected platform
	 */
	detectPlatform(req: Request | undefined, bodyPlatform?: string): Platform {
		// If bodyPlatform is provided and valid, use it
		if (bodyPlatform && this.isValidPlatform(bodyPlatform)) {
			return bodyPlatform as Platform;
		}

		// If no request, return default platform
		if (!req) {
			return Platform.WINDOWS;
		}

		// Otherwise, detect from User-Agent header
		const userAgent = req.headers['user-agent'] || '';
		// console.warn('>>>>>>>>>>>>>>> User-Agent:', userAgent);

		if (this.isIOS(userAgent)) {
			return Platform.IOS;
		}

		if (this.isAndroid(userAgent)) {
			return Platform.ANDROID;
		}

		// Default to Windows for web/desktop
		return Platform.WINDOWS;
	}

	/**
	 * Check if platform string is valid
	 */
	private isValidPlatform(platform: string): boolean {
		return Object.values(Platform).includes(platform as Platform);
	}

	/**
	 * Check if User-Agent indicates iOS device
	 */
	private isIOS(userAgent: string): boolean {
		const iosPatterns = [
			/iPhone/i,
			/iPad/i,
			/iPod/i,
			/iOS/i,
		];

		return iosPatterns.some(pattern => pattern.test(userAgent));
	}

	/**
	 * Check if User-Agent indicates Android device
	 */
	private isAndroid(userAgent: string): boolean {
		return /Android/i.test(userAgent);
	}
}