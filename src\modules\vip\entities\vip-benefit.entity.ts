// src/modules/vip/entities/vip-benefit.entity.ts
import {
	Column,
	Entity,
	PrimaryGeneratedColumn,
} from 'typeorm';

@Entity('vip_benefit')
export class VipBenefitEntity {
	@PrimaryGeneratedColumn()
	id!: number;

	@Column({ type: 'varchar' })
	name!: string;

	@Column({ type: 'text', nullable: true })
	description?: string;

	@Column({ type: 'varchar', name: 'benefit_type' })
	benefitType!: string;
}