import { AbstractDto } from '@common/dto/abstract.dto';
import { PaymentMethod, PaymentProvider, PaymentStatus, Platform } from '@constants/index';
import {
	DateField,
	EnumField,
	EnumFieldOptional,
	NumberField,
	StringField,
	StringFieldOptional,
	UUIDField,
} from '@decorators/field.decorators';

import type { PaymentTransactionEntity } from '../entities/payment-transaction.entity';

export class PaymentTransactionDto extends AbstractDto {
	@UUIDField()
	txId: string;

	@NumberField()
	userId: number;

	@StringFieldOptional()
	gameKey?: string;

	@StringField()
	packKey: string;

	@NumberField()
	amount: number;

	@StringField()
	currency: string;

	@EnumField(() => PaymentProvider)
	provider: PaymentProvider;

	@EnumFieldOptional(() => PaymentMethod)
	paymentMethod?: PaymentMethod;

	@EnumField(() => PaymentStatus)
	status: PaymentStatus;

	@StringFieldOptional()
	note?: string;

	@EnumField(() => Platform)
	platform: Platform;

	@StringFieldOptional({ isArray: false }) // nếu muốn expose
	context?: Record<string, unknown>;

	@DateField()
	createdAt: Date;

	@DateField()
	updatedAt: Date;

	constructor(paymentTransaction: PaymentTransactionEntity) {
		super(paymentTransaction);
		this.txId = paymentTransaction.txId;
		this.userId = paymentTransaction.userId;
		this.gameKey = paymentTransaction.gameKey;
		this.packKey = paymentTransaction.packKey;
		this.amount = paymentTransaction.amount;
		this.currency = paymentTransaction.currency;
		this.provider = paymentTransaction.provider as PaymentProvider;
		this.paymentMethod = paymentTransaction.paymentMethod as PaymentMethod;
		this.status = paymentTransaction.status as PaymentStatus;
		this.platform = paymentTransaction.platform as Platform;
		this.note = paymentTransaction.note;
		this.context = paymentTransaction.context;
		this.createdAt = paymentTransaction.createdAt;
		this.updatedAt = paymentTransaction.updatedAt;
	}
}
