import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class BackfillVipTierDefault1756492800001 implements MigrationInterface {
	public async up(queryRunner: QueryRunner): Promise<void> {
		// Backfill existing users with vip_tier_id = 1 if null
		await queryRunner.query(`
			UPDATE user_account
			SET vip_tier_id = 1
			WHERE vip_tier_id IS NULL
		`);

		// Set default value for vip_tier_id to 1
		await queryRunner.query(`
			ALTER TABLE user_account
			ALTER COLUMN vip_tier_id SET DEFAULT 1
		`);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		// Remove default value
		await queryRunner.query(`
			ALTER TABLE user_account
			ALTER COLUMN vip_tier_id DROP DEFAULT
		`);

		// Optionally, set back to null for backfill reversal, but since it's backfill, maybe not necessary
		// For simplicity, leave as is
	}
}
