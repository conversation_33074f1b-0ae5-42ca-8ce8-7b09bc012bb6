import { PaymentMethod, PaymentMethodStatus } from '@constants/index';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';

export class PaymentMethodDto {
	@ApiProperty({
		enum: PaymentMethod,
		description: 'Payment method',
		example: PaymentMethod.APPLEPAY,
	})
	@IsEnum(PaymentMethod)
	paymentMethod!: PaymentMethod;

	@ApiProperty({
		description: 'Payment method status',
		enum: PaymentMethodStatus,
		example: 'enabled',
	})
	@IsEnum(PaymentMethodStatus)
	status!: PaymentMethodStatus;
}

export class UpdatePaymentMethodStatusDto extends PaymentMethodDto {}

export class QueryPaymentMethodDto {
	@ApiPropertyOptional({ enum: PaymentMethodStatus })
	@IsOptional()
	@IsEnum(PaymentMethodStatus)
	status?: PaymentMethodStatus;
}
