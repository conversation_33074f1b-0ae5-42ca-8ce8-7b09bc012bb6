import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class OnepayPaymentReponseDto {

	@ApiProperty({
		description: 'Gi<PERSON> trị của vpc_Command đã gửi sang cổngthanh toán.',
		example: 'pay',
	})
	@IsString()
	vpc_Command!: string;

	@ApiProperty({
		description: 'Mã giao dịch đã gửi sang cổng.',
		example: '123456',
	})
	@IsString()
	vpc_MerchTxnRef!: string;

	@ApiProperty({
		description: 'Merchant ID đã gửi sang cổng.',
		example: 'TESTDEFAULT',
	})
	@IsString()
	vpc_Merchant!: string;

	@ApiProperty({
		description: 'Mã đơn hàng đã gửi sang cổng.',
		example: 'Ma_Don_Hang_2',
	})
	@IsString()
	vpc_OrderInfo!: string;

	@ApiProperty({
		description: '<PERSON>ố tiền thanh toán đã gửi sang cổng.',
		example: 10000,
	})
	@IsString()
	vpc_Amount!: number;

	@ApiProperty({
		description: 'Mã trạng thái giao dịch',
		example: '0',
	})
	@IsString()
	vpc_TxnResponseCode!: string;

	@ApiProperty({
		description: 'Mô tả lỗi giao dịch khi thanh toán',
		example: 'Approved',
	})
	@IsString()
	vpc_Message!: string;

	@ApiProperty({
		description: 'Phiên bản của cổng thanh toán',
		example: '2',
	})
	@IsString()
	vpc_Version!: string;

	@ApiProperty({
		description: 'Là một chuỗi duy nhất được sinh ra từ cổng thanh toán cho mỗi giao dịch.',
		example: '123456',
	})
	@IsString()
	@IsOptional()
	vpc_TransactionNo?: string;

	@ApiProperty({
		description: 'Kênh thanh toán: \n- WEB: Thanh toán qua website. \n- APP: thanh toán qua ứng dụng Mobile Banking hoặc Ví điện tử.',
		example: 'VISA',
	})
	@IsString()
	@IsOptional()
	vpc_PayChannel?: string;

	@ApiProperty({
		description: 'Loại thẻ thanh toán \n- Quốc tế: VC, MC, JC, AE, CUP \n- Nội địa: 6 số đầu định danh thẻ tham khảo phần "Danh sách mã/BIN ngân hàng nội địa". VD: 970436 (vietcombank) \n- Nội địa: 7 số gồm 6 số đầu định danh ngân hàng và mặc định thêm số 1 đằng sau. VD: 9704361(viecombank) \n- Ứng dụng thanh toán.',
		example: 'VISA',
	})
	@IsString()
	@IsOptional()
	vpc_Card?: string;

	@ApiProperty({
		description: 'Mã định danh của thẻ thanh toán',
		example: '123456',
	})
	@IsString()
	@IsOptional()
	vpc_CardUid?: string;

	@ApiProperty({
		description: 'Số thẻ thanh toán',
		example: '123456',
	})
	@IsString()
	@IsOptional()
	vpc_CardNum?: string;

	@ApiProperty({
		description: 'Tên chủ thẻ',
		example: 'John Doe',
	})
	@IsString()
	@IsOptional()
	vpc_CardHolderName?: string;

	@ApiProperty({
		description: 'Thời gian thanh toán',
		example: '2021-01-01 00:00:00',
	})
	@IsString()
	vpc_PaymentTime!: string;

	@ApiProperty({
		description: 'Với giao dịch trả góp: Ngân hàng phát hành thẻ',
		example: 'Vietcombank',
	})
	@IsString()
	@IsOptional()
	vpc_ItaBank?: string;

	@ApiProperty({
		description: 'Với giao dịch trả góp: Phí trả góp',
		example: '10000',
	})
	@IsString()
	@IsOptional()
	vpc_ItaFeeAmount?: string;

	@ApiProperty({
		description: 'Với giao dịch trả góp: Kỳ hạn trả góp',
		example: '12',
	})
	@IsString()
	@IsOptional()
	vpc_ItaTime?: string;

	@ApiProperty({
		description: 'Với giao dịch trả góp: Số điện thoại của chủ thẻ',
		example: '**********',
	})
	@IsString()
	@IsOptional()
	vpc_ItaMobile?: string;

	@ApiProperty({
		description: 'Với giao dịch trả góp: Email của chủ thẻ',
		example: '<EMAIL>',
	})
	@IsString()
	@IsOptional()
	vpc_ItaEmail?: string;

	@ApiProperty({
		description: 'Với giao dịch trả góp: Số tiền gốc của giao dịch',
		example: '10000',
	})
	@IsString()
	@IsOptional()
	vpc_OrderAmount?: string;

	@ApiProperty({
		description: 'Mã hash an toàn',
		example: 'hash',
	})
	@IsString()
	vpc_SecureHash!: string;
}
