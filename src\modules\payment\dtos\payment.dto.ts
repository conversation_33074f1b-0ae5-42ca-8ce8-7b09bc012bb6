import { PaymentStatus } from '@constants/payment';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';


export class CreateExchangePaymentDto {
	@ApiProperty({
		description: 'Game Key',
		example: 'vl1hn',
	})
	@IsString()
	gameKey!: string;

	@ApiProperty({
		description: 'Pack key',
		example: 'PACK-001',
	})
	@IsString()
	packKey!: string;
}

export class CreatePaymentFuncoinDto {
	@ApiProperty({
		description: 'Payment method',
		example: 'MOBILEBANKING',
	})
	@IsString()
	paymentMethod!: string;

	@ApiProperty({
		description: 'Pack key',
		example: 'PACK-001',
	})
	@IsString()
	packKey!: string;
}

export class CreatePaymentItemDto extends CreatePaymentFuncoinDto {
	@ApiProperty({
		description: 'Game Key',
		example: 'vl1hn',
	})
	@IsString()
	@IsOptional()
	gameKey!: string;
}

export class CreatePaymentReponseDto {
	@ApiProperty({
		description: 'Payment transaction ID',
		example: '123e4567-e89b-12d3-a456-************',
	})
	@IsString()
	txId!: string;

	@ApiProperty({
		description: 'Payment transaction URL',
		example: 'https://onepay.vn/payment/123e4567-e89b-12d3-a456-************',
	})
	@IsString()
	paymentUrl!: string;

	constructor(partial: Partial<CreatePaymentReponseDto>) {
		Object.assign(this, partial);
	}
}

export class UpdatePaymentDto {
	@ApiProperty({
		description: 'New payment status',
		enum: PaymentStatus,
		example: PaymentStatus.SUCCESS,
	})
	@IsEnum(PaymentStatus)
	status!: PaymentStatus;
}
