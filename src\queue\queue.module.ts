import { PaymentModule } from '@modules/payment/payment.module';
import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { ApiConfigService } from '@shared/services/api-config.service';

import { PaymentQueueProcessor } from './payment-queue.processor';
import { PaymentQueueService } from './payment-queue.service';

@Module({
	imports: [
		BullModule.registerQueueAsync({
			name: 'payment-queue',
			useFactory: (configService: ApiConfigService) => ({
				redis: {
					host: configService.redisConfig.socket.host,
					port: configService.redisConfig.socket.port,
				},
			}),
			inject: [ApiConfigService],
		}),
		PaymentModule,
	],
	providers: [PaymentQueueService, PaymentQueueProcessor],
	exports: [PaymentQueueService],
})
export class QueueModule {}
