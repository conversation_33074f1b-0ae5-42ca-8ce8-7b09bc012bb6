import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNumber, Min } from 'class-validator';

export class RechargeDto {
	@ApiProperty({
		description: 'User ID to recharge',
		example: 23,
	})
	@IsNumber()
	userId!: number;

	@ApiProperty({
		description: 'Amount to recharge',
		example: 500,
	})
	@IsNumber()
	@Min(0.01)
	@Type(() => Number)
	amount!: number;
}
