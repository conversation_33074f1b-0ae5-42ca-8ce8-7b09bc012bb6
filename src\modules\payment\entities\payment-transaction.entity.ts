import { AbstractEntity } from '@common/abstract.entity';
import { PaymentProvider, PaymentStatus, Platform } from '@constants/index';
import { UseDto } from '@decorators/use-dto.decorator';
import { GameEntity } from '@modules/game/game.entity';
import { UserAccountEntity } from '@modules/user/user-account.entity';
import {
	Column,
	Entity,
	Index,
	JoinColumn,
	ManyToOne,
	PrimaryGeneratedColumn,
} from 'typeorm';

import { PaymentTransactionDto } from '../dtos/payment-transaction.dto';

@Index('idx_payment_game', ['gameKey'])
@Index('idx_payment_pack_key', ['packKey'])
@Index('idx_payment_status', ['status'])
@Index('idx_payment_user', ['userId'])
@Entity('payment_transaction', { schema: 'public' })
@UseDto(PaymentTransactionDto)
export class PaymentTransactionEntity extends AbstractEntity<PaymentTransactionDto> {
	@PrimaryGeneratedColumn('uuid', { name: 'tx_id' })
	txId!: string;

	@Column('bigint', { name: 'user_id' })
	userId!: number;

	@Column('varchar', { name: 'game_key', nullable: true })
	gameKey?: string;

	@Column('varchar', { name: 'pack_key' })
	packKey!: string;

	@Column('numeric', { name: 'amount', precision: 10, scale: 2 })
	amount!: number;

	@Column('varchar', { name: 'currency', length: 10, default: 'VND' })
	currency!: string;

	@Column('varchar', {
		name: 'provider',
		length: 30,
		default: PaymentProvider.ONEPAY,
	})
	provider!: string;

	@Column('varchar', {
		name: 'payment_method',
		length: 30,
		nullable: true,
	})
	paymentMethod?: string;

	@Column('varchar', {
		name: 'status',
		length: 30,
		default: PaymentStatus.PENDING,
	})
	status!: string;

	@Column('varchar', { name: 'note', length: 500, nullable: true })
	note?: string;

	@Column('jsonb', { name: 'context', nullable: true })
	context?: Record<string, unknown>;

	@Column('varchar', {
		name: 'platform',
		length: 30,
		default: Platform.WINDOWS,
	})
	platform!: string;

	@Column('timestamp', {
		name: 'created_at',
		default: () => 'CURRENT_TIMESTAMP',
	})
	declare createdAt: Date;

	@Column('timestamp', {
		name: 'updated_at',
		default: () => 'CURRENT_TIMESTAMP',
	})
	declare updatedAt: Date;

	@ManyToOne(() => GameEntity, (game) => game.paymentTransactions)
	@JoinColumn([{ name: 'game_key', referencedColumnName: 'gameKey' }])
	game?: GameEntity;

	@ManyToOne(() => UserAccountEntity, (user) => user.paymentTransactions)
	@JoinColumn([{ name: 'user_id', referencedColumnName: 'userId' }])
	user!: UserAccountEntity;
}
