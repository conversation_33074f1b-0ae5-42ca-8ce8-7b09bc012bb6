import type { MigrationInterface, QueryRunner } from 'typeorm';

export class SeedCoinPacks1756649984446 implements MigrationInterface {
	public async up(queryRunner: QueryRunner): Promise<void> {
		// Seed FunCoin packs
		await queryRunner.query(`
      INSERT INTO funcoin_pack (pack_key, pack_name, quantity, point, price, currency, discount, tag, is_active, pack_image_url, promotion_start_date, promotion_end_date)
      VALUES
        ('FunCoin10', '10 FunCoin', 10, 10, 10000, 'VND', 5, 'HOT', true, 'https://public.fungames.vn/placeholder_square.png', '2024-01-01 00:00:00', '2025-01-31 23:59:59'),
        ('FunCoin20', '20 FunCoin', 20, 20, 20000, 'VND', 5, 'SALE', true, 'https://public.fungames.vn/placeholder_square.png', '2024-02-01 00:00:00', '2025-02-28 23:59:59'),
        ('FunCoin50', '50 FunCoin', 50, 50, 50000, 'VND', 10, 'SALE', true, 'https://public.fungames.vn/placeholder_square.png', NULL, NULL),
        ('FunCoin100', '100 FunCoin', 100, 100, 100000, 'VND', 10, 'HOT', true, 'https://public.fungames.vn/placeholder_square.png', '2024-01-01 00:00:00', '2025-12-31 23:59:59'),
        ('FunCoin200', '200 FunCoin', 200, 200, 200000, 'VND', 10, 'SALE', true, 'https://public.fungames.vn/placeholder_square.png', '2024-02-01 00:00:00', '2025-12-28 23:59:59'),
        ('FunCoin500', '500 FunCoin', 500, 500, 500000, 'VND', 10, 'SALE', true, 'https://public.fungames.vn/placeholder_square.png', NULL, NULL),
        ('FunCoin1k', '1000 FunCoin', 1000, 1000, 1000000, 'VND', 10, 'NEW', true, 'https://public.fungames.vn/placeholder_square.png', '2024-03-01 00:00:00', '2025-12-31 23:59:59'),
        ('FunCoin2k', '2000 FunCoin', 2000, 2000, 2000000, 'VND', 10, 'NEW', true, 'https://public.fungames.vn/placeholder_square.png', '2024-03-01 00:00:00', '2025-12-31 23:59:59'),
        ('FunCoin5k', '5000 FunCoin', 5000, 5000, 5000000, 'VND', 10, 'NEW', true, 'https://public.fungames.vn/placeholder_square.png', '2024-03-01 00:00:00', '2025-12-31 23:59:59')
    `);

		// Seed Item packs
		await queryRunner.query(`
      INSERT INTO item_pack (pack_key, pack_name, quantity, point, price, currency, discount, tag, is_active, pay_with, game_key, pack_image_url, promotion_start_date, promotion_end_date)
      VALUES
        ('CASH_KIM_CUONG_10', 'Kim Cuong 10', 10, 100, 100000, 'VND', 5, 'NEW', true, 'CASH', 'vl1hn', 'https://public.fungames.vn/placeholder_square.png', '2024-01-15 00:00:00', '2026-01-31 23:59:59'),
        ('CASH_KIM_CUONG_100', 'Kim Cuong 100', 100, 1000, 1000000, 'VND', 10, 'NEW', true, 'CASH', 'vl1hn', 'https://public.fungames.vn/placeholder_square.png', NULL, NULL),
        ('FunCoin_KIM_CUONG_10', 'Kim Cuong 10', 10, 100, 100, 'FUNCOIN', 5, 'HOT', true, 'FUNCOIN', 'vl1hn', 'https://public.fungames.vn/placeholder_square.png', '2024-02-01 00:00:00', '2026-02-15 23:59:59'),
        ('FunCoin_KIM_CUONG_100', 'Kim Cuong 100', 100, 1000, 1000, 'FUNCOIN', 5, 'SALE', true, 'FUNCOIN', 'vl1hn', 'https://public.fungames.vn/placeholder_square.png', NULL, NULL),
        ('CASH_GOLD_10', 'Vang 10', 10, 100, 100000, 'VND', 5, 'NEW', true, 'CASH', 'vl2hn', 'https://public.fungames.vn/placeholder_square.png', '2024-03-01 00:00:00', '2026-03-15 23:59:59'),
        ('CASH_GOLD_100', 'Vang 100', 100, 1000, 1000000, 'VND', 10, 'NEW', true, 'CASH', 'vl2hn', 'https://public.fungames.vn/placeholder_square.png', NULL, NULL),
        ('FunCoin_GOLD_10', 'Vang 10', 10, 100, 100, 'FUNCOIN', 5, 'HOT', true, 'FUNCOIN', 'vl2hn', 'https://public.fungames.vn/placeholder_square.png', '2024-04-01 00:00:00', '2025-04-30 23:59:59'),
        ('FunCoin_GOLD_100', 'Vang 100', 100, 1000, 1000, 'FUNCOIN', 10, 'SALE', true, 'FUNCOIN', 'vl2hn', 'https://public.fungames.vn/placeholder_square.png', NULL, NULL)
    `);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		// Rollback FunCoin packs
		await queryRunner.query(`
      DELETE FROM funcoin_pack 
      WHERE pack_key IN (
        'FunCoin10',
        'FunCoin20',
        'FunCoin50',
        'FunCoin100',
        'FunCoin200',
        'FunCoin500',
        'FunCoin1k', 
        'FunCoin2k', 
        'FunCoin5k'
      )
    `);

		// Rollback Item packs
		await queryRunner.query(`
      DELETE FROM item_pack 
      WHERE pack_key IN (
        'CASH_KIM_CUONG_10',
        'CASH_KIM_CUONG_100',
        'FunCoin_KIM_CUONG_10',
        'FunCoin_KIM_CUONG_100',
        'CASH_GOLD_10',
        'CASH_GOLD_100',
        'FunCoin_GOLD_10',
        'FunCoin_GOLD_100'
      )
    `);
	}
}
