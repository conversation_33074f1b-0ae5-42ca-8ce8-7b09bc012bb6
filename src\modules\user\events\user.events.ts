
// d:\codes\fs_player_service\src\modules\user\events\user.events.ts
import type { Platform } from '@constants/platform';

import { MutateUserProfileDto } from '../dtos/mutate-user-profile.dto';

export class UserProfileUpdatedEvent {
	constructor(
		public readonly userId: number,
		public readonly ip: string,
		public readonly oldProfile: Record<string, unknown>,
		public readonly newProfile: MutateUserProfileDto,
		public readonly platform: Platform,
	) {}
}
