// src/modules/vip/vip.module.ts
import { UserAccountEntity } from '@modules/user/user-account.entity';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { HelpersModule } from '../../common/helpers/helpers.module';
import { SharedModule } from '../../shared/shared.module';
import { VipBenefitEntity } from './entities/vip-benefit.entity';
import { VipTierEntity } from './entities/vip-tier.entity';
import { VipController } from './vip.controller';
import { VipService } from './vip.service';

@Module({
	imports: [
		TypeOrmModule.forFeature([
			VipTierEntity,
			VipBenefitEntity,
			UserAccountEntity,
		]),
		HelpersModule,
		SharedModule,
	],
	controllers: [VipController],
	providers: [VipService],
	exports: [VipService],
})
export class VipModule {}
