import { <PERSON><PERSON><PERSON><PERSON>per } from '@common/helpers/cache.helper';
import { UserAccountEntity } from '@modules/user/user-account.entity';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { RedisService } from '@shared/services/redis.service';
import { RedisKeyManagerService } from '@shared/services/redis-key-manager.service';
import {
	UserNotFoundException,
} from 'exceptions';
import { In, Repository } from 'typeorm';

import type { VipTierDto } from './dto/vip-tier.dto';
import { VipBenefitEntity } from './entities/vip-benefit.entity';
import { VipTierEntity } from './entities/vip-tier.entity';

@Injectable()
export class VipService {
	constructor(
		@InjectRepository(VipTierEntity)
		private readonly vipTierRepository: Repository<VipTierEntity>,
		@InjectRepository(VipBenefitEntity)
		private readonly vipBenefitRepository: Repository<VipBenefitEntity>,
		@InjectRepository(UserAccountEntity)
		private readonly userAccountRepository: Repository<UserAccountEntity>,
		private readonly cacheHelper: CacheHelper,
		private readonly redisService: RedisService,
		private readonly redisKeyManager: RedisKeyManagerService,
	) {}

	// // VIP Tier CRUD
	// async createVipTier(createVipTierDto: CreateVipTierDto): Promise<VipTierEntity> {
	// 	const { benefits, ...tierData } = createVipTierDto;
    
	// 	const vipTier = this.vipTierRepository.create(tierData);
		
	// 	// Convert benefits array to JSON string
	// 	if (benefits && benefits.length > 0) {
	// 		// Validate benefits exist
	// 		const benefitEntities = await this.vipBenefitRepository.findBy({ id: In(benefits) });
	// 		if (benefitEntities.length !== benefits.length) {
	// 			throw new VipBenefitNotFoundException('Some benefits not found');
	// 		}
	// 		vipTier.benefits = JSON.stringify(benefits);
	// 	} else {
	// 		vipTier.benefits = JSON.stringify([]);
	// 	}

	// 	return await this.vipTierRepository.save(vipTier);
	// }

	// async findVipTierById(id: number): Promise<VipTierEntity> {
	// 	const cacheKey = this.redisKeyManager.vip.tier(id);

	// 	return await this.cacheHelper.getWithFallback(
	// 		cacheKey,
	// 		async() => {
	// 			const vipTier = await this.vipTierRepository.findOne({
	// 				where: { id },
	// 			});

	// 			if (!vipTier) {
	// 				throw new VipTierNotFoundException(`VIP Tier with ID ${id} not found`);
	// 			}
				
	// 			// Parse benefits
	// 			vipTier.benefits = JSON.parse(vipTier.benefits || '[]');

	// 			return vipTier;
	// 		},
	// 		3600, // 1 hour TTL
	// 		'vip-tier',
	// 	);
	// }

	// async updateVipTier(id: number, updateVipTierDto: UpdateVipTierDto): Promise<VipTierEntity> {
	// 	const vipTier = await this.findVipTierById(id);
	// 	const { benefits, ...updateData } = updateVipTierDto;

	// 	Object.assign(vipTier, updateData);

	// 	if (benefits !== undefined) {
	// 		if (benefits.length > 0) {
	// 			const benefitEntities = await this.vipBenefitRepository.findBy({
	// 				id: In(benefits),
	// 			});
	// 			if (benefitEntities.length !== benefits.length) {
	// 				throw new VipBenefitNotFoundException('Some benefits not found');
	// 			}
	// 			vipTier.benefits = JSON.stringify(benefits);
	// 		} else {
	// 			vipTier.benefits = JSON.stringify([]);
	// 		}
	// 	}

	// 	const savedVipTier = await this.vipTierRepository.save(vipTier);

	// 	// Thêm dòng này để xóa cache
	// 	await this.invalidateVipTierCache(id);

	// 	return savedVipTier;
	// }

	// async deleteVipTier(id: number): Promise<void> {
	// 	const vipTier = await this.findVipTierById(id);
	// 	await this.vipTierRepository.remove(vipTier);
	// 	// Thêm dòng này để xóa cache
	// 	await this.invalidateVipTierCache(id);
	// }

	// // Benefit CRUD
	// async createVipBenefit(createVipBenefitDto: CreateVipBenefitDto): Promise<VipBenefitEntity> {
	// 	const benefit = this.vipBenefitRepository.create(createVipBenefitDto);
	// 	return await this.vipBenefitRepository.save(benefit);
	// }

	// async findAllVipBenefits(): Promise<VipBenefitEntity[]> {
	// 	return await this.vipBenefitRepository.find({
	// 		order: { id: 'ASC' },
	// 	});
	// }

	// async findVipBenefitById(id: number): Promise<VipBenefitEntity> {
	// 	const benefit = await this.vipBenefitRepository.findOne({
	// 		where: { id },
	// 	});

	// 	if (!benefit) {
	// 		throw new VipBenefitNotFoundException(`Benefit with ID ${id} not found`);
	// 	}

	// 	return benefit;
	// }

	// async updateVipBenefit(
	// 	id: number,
	// 	updateVipBenefitDto: UpdateVipBenefitDto,
	// ): Promise<VipBenefitEntity> {
	// 	const benefit = await this.findVipBenefitById(id);
	// 	Object.assign(benefit, updateVipBenefitDto);
	// 	return await this.vipBenefitRepository.save(benefit);
	// }

	// async deleteVipBenefit(id: number): Promise<void> {
	// 	const benefit = await this.findVipBenefitById(id);
	// 	await this.vipBenefitRepository.remove(benefit);
	// }

	async findAllVipTiers(): Promise<VipTierDto[]> {
		const cacheKey = this.redisKeyManager.vip.tiers();

		return await this.cacheHelper.getWithFallback(
			cacheKey,
			async() => {
				const vipTiers = await this.vipTierRepository.find({
					order: { value: 'ASC' },
				});

				return Promise.all(vipTiers.map(async(tier) => {
					return {
						id: tier.id,
						tierName: tier.tierName,
						description: tier.description,
						value: tier.value,
						benefits: (await this.getVipBenefitsByIds(JSON.parse(tier.benefits))),
					};
				}));
			},
			86400, // 1 day TTL
			'all-vip-tiers',
		);
	}

	// User VIP Operations
	async updateUserRecharge(userId: number, amount: number){
		const user = await this.userAccountRepository.findOne({
			where: { userId },
			relations: ['vipTier'],
		});

		if (!user) {
			throw new UserNotFoundException(`User with ID ${userId} not found`);
		}

		// Update total recharge
		user.totalRecharge = Number(user.totalRecharge) + Number(amount);

		// Calculate new VIP tier based on total recharge
		const oldVipTier = user.vipTier;
		const newVipTier = await this.calculateVipTier(user.totalRecharge);
		user.vipTier = newVipTier;

		const saveUser = await this.userAccountRepository.save(user);
		await this.invalidateUserVipCache(userId);
		return {
			user_id: user.userId,
			old_vip_tier: oldVipTier?.tierName,
			recharge_amount: amount,
			total_recharge: saveUser.totalRecharge,
			current_vip_tier: newVipTier?.tierName,
		};
	}

	private async calculateVipTier(totalRecharge: number): Promise<VipTierEntity | undefined> {
		const vipTiers = await this.vipTierRepository.find({
			order: { value: 'DESC' },
		});

		for (const tier of vipTiers) {
			if (Number(totalRecharge) >= Number(tier.value)) {
				// Parse benefits before returning
				tier.benefits = JSON.parse(tier.benefits || '[]');
				return tier;
			}
		}

		return undefined;
	}

	// Cache invalidation methods
	async invalidateVipTierCache(id: number): Promise<void> {
		const tierKey = this.redisKeyManager.vip.tier(id);
		const tiersKey = this.redisKeyManager.vip.tiers();
		await this.redisService.del(tierKey);
		await this.redisService.del(tiersKey);
	}

	async invalidateUserVipCache(userId: number): Promise<void> {
		const userKey = this.redisKeyManager.vip.user(userId);
		await this.redisService.del(userKey);
	}

	private async getVipBenefitsByIds(ids: number[]): Promise<VipBenefitEntity[]> {
		if (ids.length === 0) {
			return [];
		}
		
		return await this.vipBenefitRepository.findBy({ id: In(ids) });
	}
}