import { InvalidDtoException } from 'exceptions';
import {
	CreateDateColumn,
	//   PrimaryGeneratedColumn,
	UpdateDateColumn,
} from 'typeorm';

import type {
	AbstractDto,
} from './dto/abstract.dto.ts';

/**
 * Abstract Entity
 * <AUTHOR> <<EMAIL>>
 *
 * @description This class is an abstract class for all entities.
 * It's experimental and recommended using it only in microservice architecture,
 * otherwise just delete and use your own entity.
 */
export abstract class AbstractEntity<
	DTO extends AbstractDto = AbstractDto,
	O = never,
> {
	//   @PrimaryGeneratedColumn('uuid')
	//   id!: Uuid;

	@CreateDateColumn({
		type: 'timestamp',
	})
	createdAt!: Date;

	@UpdateDateColumn({
		type: 'timestamp',
	})
	updatedAt!: Date;

	toDto(options?: O): DTO {
		const dtoClass = Object.getPrototypeOf(this).dtoClass;

		if (!dtoClass) {
			throw new InvalidDtoException(
				`You need to use @UseDto on class (${this.constructor.name}) be able to call toDto function`,
			);
		}

		return new dtoClass(this, options);
	}
}
