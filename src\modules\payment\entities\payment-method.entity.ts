import { PaymentMethod, PaymentMethodStatus } from '@constants/index';
import { Column, Entity, PrimaryColumn } from 'typeorm';


@Entity({ name: 'payment_method' })
export class PaymentMethodEntity {
	@PrimaryColumn({
		type: 'enum',
		name: 'payment_method',
		enum: PaymentMethod,
		unique: true,
	})
	paymentMethod!: PaymentMethod;

	@Column({
		type: 'enum',
		enum: PaymentMethodStatus,
		default: PaymentMethodStatus.ENABLED,
	})
	status!: PaymentMethodStatus;
}