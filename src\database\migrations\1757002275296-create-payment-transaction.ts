import type { MigrationInterface, QueryRunner } from 'typeorm';

export class CreatePaymentTransaction1757002275296 implements MigrationInterface {
	name = 'CreatePaymentTransaction1757002275296';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
			CREATE TABLE "payment_transaction" (
				"tx_id" uuid NOT NULL DEFAULT gen_random_uuid(),
				"user_id" bigint NOT NULL,
				"game_key" character varying,
				"pack_key" character varying NOT NULL,
				"amount" numeric(10,2) NOT NULL,
				"currency" character varying(10) NOT NULL DEFAULT 'VND',
				"provider" character varying(30) NOT NULL DEFAULT 'onepay',
				"payment_method" character varying(30),
				"status" character varying(30) NOT NULL DEFAULT 'pending',
				"note" character varying(500),
				"context" jsonb,
				"platform" character varying(30) NOT NULL DEFAULT 'windows',
				"created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
				"updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
				CONSTRAINT "PK_43d7c92466f8b34e443d9bc0a03" PRIMARY KEY ("tx_id")
			)
		`);
		await queryRunner.query(
			'CREATE INDEX "idx_payment_user" ON "payment_transaction" ("user_id")',
		);
		await queryRunner.query(
			'CREATE INDEX "idx_payment_status" ON "payment_transaction" ("status")',
		);
		await queryRunner.query(
			'CREATE INDEX "idx_payment_game" ON "payment_transaction" ("game_key")',
		);
		await queryRunner.query(
			'CREATE INDEX "payment_transaction_pack_key" ON "payment_transaction" ("pack_key")',
		);
		await queryRunner.query(
			'CREATE UNIQUE INDEX "payment_transaction_pkey" ON "payment_transaction" ("tx_id")',
		);
		// Add foreign keys
		await queryRunner.query(
			'ALTER TABLE "payment_transaction" ADD CONSTRAINT "FK_payment_transaction_game" FOREIGN KEY ("game_key") REFERENCES "game"("game_key") ON DELETE NO ACTION ON UPDATE NO ACTION',
		);
		await queryRunner.query(
			'ALTER TABLE "payment_transaction" ADD CONSTRAINT "FK_payment_transaction_user_account" FOREIGN KEY ("user_id") REFERENCES "user_account"("user_id") ON DELETE NO ACTION ON UPDATE NO ACTION',
		);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query('DROP TABLE "payment_transaction"');
	}
}
