import { Injectable } from '@nestjs/common';
// import axios from 'axios';
import * as CryptoJS from 'crypto-js';

import { OnepayParamDto } from './dto/onepay-param.dto';
import { OnepayPaymentReponseDto } from './dto/onepay-payment-reponse.dto';
import { OnepayPaymentRequestDto } from './dto/onepay-payment-request.dto';

type Json = Record<string, string>;

@Injectable()
export class OnepayService {

	async makeUrlRequest(onepayParamDto: OnepayParamDto): Promise<string> {
		const param = this.merchantSendRequestDynamic(onepayParamDto);
		console.info(param);
		const url = `${process.env.ONEPAY_BASE_URL}?${this.getQueryString(param)}`;
		console.info('url: ' + url);
		return url;
	}

	public verifyResponse(onepayResponse: OnepayPaymentReponseDto) {
		const hashFromMerchant = onepayResponse.vpc_SecureHash;
		const paramsSorted = this.sortObj(onepayResponse);
		const stringToHash = this.generateStringToHash(paramsSorted);
		const OnePaySign = this.genSecureHash(stringToHash);
		// console.info('onepay hash: ' + OnePaySign);
		// console.info('merchant hash: ' + hashFromMerchant);
		return OnePaySign == hashFromMerchant;
	}

	private merchantSendRequestDynamic(onepayParamDto: OnepayParamDto): OnepayPaymentRequestDto {
		const merchantParam: OnepayPaymentRequestDto = {
			vpc_Version: '2',
			vpc_Command: 'pay',
			vpc_AccessCode: process.env.ONEPAY_ACCESS_CODE || '',
			vpc_Merchant: process.env.ONEPAY_MERCHANT_ID || '',
			vpc_ReturnURL: process.env.ONEPAY_RETURN_URL || '',
			vpc_CallbackURL: process.env.ONEPAY_IPN_URL || '',
			vpc_Locale: 'vn',
			// custom from here
			...onepayParamDto,
			// vpc_MerchTxnRef: 'TEST_' + Date.now(), // txId
			// vpc_OrderInfo: 'Ma_Don_Hang_2', // packKey
			// vpc_Amount: '1000000', // amount
			// vpc_TicketNo: '***************', // ipAddress
			// vpc_CardList: 'MOBILEBANKING', // method
			// vpc_Customer_Phone: '***********', // phone
			// vpc_Customer_Email: '<EMAIL>', // email
			// vpc_Customer_Id: 'test', // userId
		};
		const sortedParam = this.sortObj(merchantParam);
		const stringToHash = this.generateStringToHash(sortedParam);
		const secureHash = this.genSecureHash(stringToHash);
		merchantParam['vpc_SecureHash'] = secureHash;
		console.info('StringToHash: ' + stringToHash);
		console.info('merchantParam: ' + merchantParam);
		return merchantParam;
	}

	private getQueryString(dto: OnepayPaymentRequestDto): string {
		return new URLSearchParams(
			Object.entries(dto).reduce((acc, [key, value]) => {
				if (value !== undefined && value !== null) {
					acc[key] = String(value); // ensure string
				}
				return acc;
			}, {} as Record<string, string>),
		).toString();
	}

	private sortObj(merchantParam: OnepayPaymentRequestDto | OnepayPaymentReponseDto): Json {
		const obj = JSON.parse(JSON.stringify(merchantParam));
		return Object.keys(obj)
			.sort()
			.reduce((result: Json, key: string) => {
				result[key] = obj[key];
				return result;
			}, {});
	}

	private generateStringToHash(paramSorted: Json) {
		let stringToHash = '';
		for (const key in paramSorted) {
			const value = paramSorted[key];
			const pref4 = key.substring(0, 4);
			const pref5 = key.substring(0, 5);
			if (pref4 == 'vpc_' || pref5 == 'user_') {
				if (key != 'vpc_SecureHash' && key != 'vpc_SecureHashType') {
					if (String(value).length > 0) {
						if (stringToHash.length > 0) {
							stringToHash = stringToHash + '&';
						}
						stringToHash = stringToHash + key + '=' + value;
					}
				}
			}
		}
		return stringToHash;
	}

	private genSecureHash(stringToHash: string) {
		const merHashHex = CryptoJS.enc.Hex.parse(process.env.ONEPAY_HASH_CODE || '');
		const keyHash = CryptoJS.HmacSHA256(stringToHash, merHashHex);
		const keyHashHex = CryptoJS.enc.Hex.stringify(keyHash).toUpperCase();
		return keyHashHex;
	}
}
