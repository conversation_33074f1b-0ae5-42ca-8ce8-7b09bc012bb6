// src/modules/vip/entities/vip-tier.entity.ts
import { UserAccountEntity } from '@modules/user/user-account.entity';
import {
	Column,
	Entity,
	OneToMany,
	PrimaryGeneratedColumn,
} from 'typeorm';


@Entity('vip_tier')
export class VipTierEntity {
	@PrimaryGeneratedColumn()
	id!: number;

	@Column({ type: 'varchar', unique: true, name: 'tier_name' })
	tierName!: string;

	@Column({ type: 'text', nullable: true })
	description?: string;

	@Column({ type: 'decimal', precision: 15, scale: 2 })
	value!: number;

	// Store benefit IDs as a JSON string
	@Column({ type: 'text', nullable: true })
	benefits!: string;

	@OneToMany(() => UserAccountEntity, (user) => user.vipTier)
	users!: UserAccountEntity[];
}