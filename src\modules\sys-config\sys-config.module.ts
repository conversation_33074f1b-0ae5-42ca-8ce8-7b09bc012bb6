import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RedisService } from '@shared/services/redis.service';

import { SysConfigEntity } from './entities/sys-config.entity';
import { SysConfigService } from './sys-config.service';

@Module({
	imports: [
		TypeOrmModule.forFeature([SysConfigEntity]),
	],
	// TODO: enable for admin
	// controllers: [SysConfigController],
	providers: [SysConfigService, RedisService],
	exports: [SysConfigService],
})
export class SysConfigModule {}
