import { BooleanFieldOptional, EmailOrPhoneField } from '@decorators/field.decorators';

export class ContactVerifyDto {
	@EmailOrPhoneField({
		example: '<EMAIL> or 84905060708',
		description: 'Email address or Vietnamese phone number to verify',
	})
	contact!: string;

	@BooleanFieldOptional({
		example: false,
		description: 'Is SSO user. This is optional and default to false',
	})
	isSso?: boolean;
}
