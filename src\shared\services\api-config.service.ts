import { AdminAccountEntity } from '@modules/admin/admin-account.entity';
import { AdminRoleMappingEntity } from '@modules/admin/admin-role-mapping.entity';
import { RoleEntity } from '@modules/admin/role.entity';
import { AuditLogEntity } from '@modules/audit-log/entities/audit-log.entity';
import { GameEntity } from '@modules/game/game.entity';
import { FunCoinPack } from '@modules/payment/entities/funcoin-pack.entity.ts';
import { ItemPack } from '@modules/payment/entities/item-pack.entity';
import { PaymentMethodEntity } from '@modules/payment/entities/payment-method.entity.ts';
import { PaymentTransactionEntity } from '@modules/payment/entities/payment-transaction.entity.ts';
import { QuickplayEntity } from '@modules/quickplay/quickplay.entity';
import { SysConfigEntity } from '@modules/sys-config/entities/sys-config.entity';
import { UserAccountEntity } from '@modules/user/user-account.entity';
import { UserGameMappingEntity } from '@modules/user/user-game-mapping.entity';
import { UserProfileEntity } from '@modules/user/user-profile.entity';
import { VipBenefitEntity } from '@modules/vip/entities/vip-benefit.entity';
import { VipTierEntity } from '@modules/vip/entities/vip-tier.entity';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import type { ThrottlerOptions } from '@nestjs/throttler';
import type { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { ConfigNotFoundException, TypeException } from 'exceptions';
import parse from 'parse-duration';

import { InitTables21749973125057 } from '../../database/migrations/*************-init-tables.ts';
import { UserSubscriber } from '../../entity-subscribers/user-subscriber.ts';
import { SnakeNamingStrategy } from '../../snake-naming.strategy.ts';

@Injectable()
export class ApiConfigService {
	constructor(private configService: ConfigService) {}

	get isDevelopment(): boolean {
		return this.nodeEnv === 'development';
	}

	get isProduction(): boolean {
		return this.nodeEnv === 'production';
	}

	get isTest(): boolean {
		return this.nodeEnv === 'test';
	}

	public getNumber(key: string): number {
		const value = this.get(key);
		const num = Number(value);

		if (Number.isNaN(num)) {
			throw new TypeException(
				`Environment variable ${key} must be a number. Received: ${value}`,
			);
		}

		return num;
	}

	private getDuration(
		key: string,
		format?: Parameters<typeof parse>[1],
	): number {
		const value = this.getString(key);
		const duration = parse(value, format);

		if (duration === null) {
			throw new ConfigNotFoundException(
				`Environment variable ${key} must be a valid duration. Received: ${value}`,
			);
		}

		return duration;
	}

	private getBoolean(key: string): boolean {
		const value = this.get(key);

		try {
			return Boolean(JSON.parse(value));
		} catch {
			throw new ConfigNotFoundException(
				`Environment variable ${key} must be a boolean. Received: ${value}`,
			);
		}
	}

	public getString(key: string): string {
		const value = this.get(key);

		return value.replaceAll(String.raw`\n`, '\n');
	}

	get nodeEnv(): string {
		return this.getString('NODE_ENV');
	}

	get throttlerConfigs(): ThrottlerOptions {
		return {
			ttl: this.getDuration('THROTTLER_TTL', 'second'),
			limit: this.getNumber('THROTTLER_LIMIT'),
			// storage: new ThrottlerStorageRedisService(new Redis(this.redis)),
		};
	}

	get postgresConfig(): TypeOrmModuleOptions {
		const entities = [
			//   path.join(import.meta.dirname, '/../**/*.entity{.ts,.js}'),
			//   path.join(import.meta.dirname, 'src/**/*.entity.ts'),

			AdminAccountEntity,
			AdminRoleMappingEntity,
			AuditLogEntity,
			GameEntity,
			PaymentTransactionEntity,
			QuickplayEntity,
			RoleEntity,
			SysConfigEntity,
			UserAccountEntity,
			UserGameMappingEntity,
			UserProfileEntity,
			VipBenefitEntity,
			VipTierEntity,
			FunCoinPack,
			ItemPack,
			PaymentMethodEntity,
		];

		const migrations = [
			//   path.join(import.meta.dirname, `../../database/migrations/*{.ts,.js}`),
			InitTables21749973125057,
		];
		const subscribers = [UserSubscriber];

		return {
			entities,
			migrations,
			dropSchema: this.isTest,
			type: 'postgres',
			host: this.getString('DB_HOST'),
			port: this.getNumber('DB_PORT'),
			username: this.getString('DB_USERNAME'),
			password: this.getString('DB_PASSWORD'),
			database: this.getString('DB_DATABASE'),
			subscribers,
			migrationsRun: true,
			logging: this.getBoolean('ENABLE_ORM_LOGS'),
			namingStrategy: new SnakeNamingStrategy(),
		};
	}

	get redisConfig(): { socket: { host: string; port: number }; ttl: number } {
		const host =
			this.configService.get<string>('REDIS_HOST') ||
			this.configService.get<string>('INTERNAL_REDIS_HOST') ||
			'localhost';
		const port =
			this.configService.get<number>('REDIS_PORT') ||
			this.configService.get<number>('INTERNAL_REDIS_PORT') ||
			6379;
		const ttl = this.configService.get<number>('REDIS_TTL') || 600;

		return {
			socket: {
				host,
				port,
			},
			ttl,
		};
	}

	get documentationEnabled(): boolean {
		return this.getBoolean('ENABLE_DOCUMENTATION');
	}

	get authConfig() {
		return {
			privateKey: this.getString('JWT_PRIVATE_KEY'),
			publicKey: this.getString('JWT_PUBLIC_KEY'),
			jwtExpirationTime: this.getNumber('JWT_EXPIRATION_TIME'),
			jwtRefreshTokenExpirationTime: this.getNumber(
				'JWT_REFRESH_TOKEN_EXPIRATION_TIME',
			),
		};
	}

	get appConfig() {
		return {
			port: this.getString('PORT'),
			portPayment: this.getString('PORT_PAYMENT'),
		};
	}

	private get(key: string): string {
		const value = this.configService.get<string>(key);

		if (value == null) {
			throw new ConfigNotFoundException(`Environment variable ${key} is not set`);
		}

		return value;
	}

	get apiVersion(): string {
		return this.getString('API_VERSION');
	}

	get frontendUrl(): string {
		return this.getString('FRONTEND_URL');
	}

	get backendUrl(): string {
		return this.getString('BACKEND_URL');
	}

	get facebookCert(): { clientID: string; clientSecret: string } {
		return {
			clientID: this.getString('FACEBOOK_CLIENT_ID'),
			clientSecret: this.getString('FACEBOOK_CLIENT_SECRET'),
		};
	}

	get googleCert(): { clientID: string; clientSecret: string } {
		return {
			clientID: this.getString('GOOGLE_CLIENT_ID'),
			clientSecret: this.getString('GOOGLE_CLIENT_SECRET'),
		};
	}

	get zaloCert(): { clientID: string; clientSecret: string } {
		return {
			clientID: this.getString('ZALO_CLIENT_ID'),
			clientSecret: this.getString('ZALO_CLIENT_SECRET'),
		};
	}

	get auditLogDatabaseEnabled(): boolean {
		return this.getBoolean('AUDIT_LOG_DATABASE_ENABLED');
	}
	get auditLogRedisCacheEnabled(): boolean {
		return this.getBoolean('AUDIT_LOG_REDIS_CACHE_ENABLED');
	}
}
