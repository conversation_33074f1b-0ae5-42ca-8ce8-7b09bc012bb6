import type { MigrationInterface, QueryRunner } from 'typeorm';

export class AddApiFieldsToGameTable1757002275292 implements MigrationInterface {
	name = 'AddApiFieldsToGameTable1757002275292';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
			ALTER TABLE "game" ADD COLUMN "api_endpoint" character varying(255);
		`);

		await queryRunner.query(`
			ALTER TABLE "game" ADD COLUMN "is_api_active" boolean NOT NULL DEFAULT true;
		`);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
			ALTER TABLE "game" DROP COLUMN "is_api_active";
		`);

		await queryRunner.query(`
			ALTER TABLE "game" DROP COLUMN "api_endpoint";
		`);
	}
}
