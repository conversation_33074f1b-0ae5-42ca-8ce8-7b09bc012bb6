import { Injectable } from '@nestjs/common';
import type { IEventHandler } from '@nestjs/cqrs';
import { EventsHandler } from '@nestjs/cqrs';

import { maskId } from '../../../common/utils';
import { AuditLogLevel } from '../../../constants/audit-log';
import {
	PasswordChangedEvent,
	PasswordResetEvent,
	RefreshTokenFailedEvent,
	RefreshTokenSuccessEvent,
	UserLoggedInEvent,
	UserLoggedOutEvent,
	UserRegisteredEvent,
	VerifyContactEvent,
} from '../../auth/events/auth.events';
import { UserProfileUpdatedEvent } from '../../user/events/user.events';
import { AuditLogService } from '../audit-log.service';

// Mỗi handler sẽ xử lý một event cụ thể

@Injectable()
@EventsHandler(UserLoggedInEvent)
export class UserLoggedInHandler implements IEventHandler<UserLoggedInEvent> {
	constructor(private readonly auditLogService: AuditLogService) {}

	handle(event: UserLoggedInEvent) {
		this.auditLogService.logAuth(
			'login_success',
			event.userId,
			{
				ip: event.ip,
				context: { loginMethod: event.loginMethod },
			},
			event.platform,
		);
	}
}

@Injectable()
@EventsHandler(UserRegisteredEvent)
export class UserRegisteredHandler implements IEventHandler<UserRegisteredEvent> {
	constructor(private readonly auditLogService: AuditLogService) {}

	handle(event: UserRegisteredEvent) {
		this.auditLogService.logAuth(
			'register_success',
			event.userId,
			{
				ip: event.ip,
				context: { registrationMethod: event.registrationMethod },
			},
			event.platform,
		);
	}
}

@Injectable()
@EventsHandler(UserLoggedOutEvent)
export class UserLoggedOutHandler implements IEventHandler<UserLoggedOutEvent> {
	constructor(private readonly auditLogService: AuditLogService) {}

	handle(event: UserLoggedOutEvent) {
		this.auditLogService.logAuth('logout_success', event.userId, {
			ip: event.ip,
		});
	}
}

@Injectable()
@EventsHandler(PasswordChangedEvent)
export class PasswordChangedHandler implements IEventHandler<PasswordChangedEvent> {
	constructor(private readonly auditLogService: AuditLogService) {}

	handle(event: PasswordChangedEvent) {
		this.auditLogService.logSecurity('password_change_success', event.userId, {
			ip: event.ip,
		});
	}
}

@Injectable()
@EventsHandler(PasswordResetEvent)
export class PasswordResetHandler implements IEventHandler<PasswordResetEvent> {
	constructor(private readonly auditLogService: AuditLogService) {}

	handle(event: PasswordResetEvent) {
		this.auditLogService.logAuth('reset_password_success', event.userId, {
			ip: event.ip,
		});
	}
}

@Injectable()
@EventsHandler(RefreshTokenSuccessEvent)
export class RefreshTokenSuccessHandler implements IEventHandler<RefreshTokenSuccessEvent> {
	constructor(private readonly auditLogService: AuditLogService) {}

	handle(event: RefreshTokenSuccessEvent) {
		this.auditLogService.logAuth('refresh_token_success', event.userId);
	}
}

@Injectable()
@EventsHandler(RefreshTokenFailedEvent)
export class RefreshTokenFailedHandler implements IEventHandler<RefreshTokenFailedEvent> {
	constructor(private readonly auditLogService: AuditLogService) {}

	handle(event: RefreshTokenFailedEvent) {
		this.auditLogService.logAuth('refresh_token_failed', event.userId, {
			level: AuditLogLevel.WARNING,
			success: false,
			errorMessage: event.reason,
			context: { error: event.error?.message },
		});
	}
}

@Injectable()
@EventsHandler(UserProfileUpdatedEvent)
export class UserProfileUpdatedHandler implements IEventHandler<UserProfileUpdatedEvent> {
	constructor(private readonly auditLogService: AuditLogService) {}

	handle(event: UserProfileUpdatedEvent) {
		const { newProfile } = event;

		if (newProfile.identifierNumber) {
			newProfile.identifierNumber = maskId(newProfile.identifierNumber ?? '', 0, 4);
		}

		this.auditLogService.logUserManagement(
			'user_profile_updated',
			event.userId,
			{
				ip: event.ip,
				context: {
					oldValues: event.oldProfile,
					newValues: newProfile,
				},
			},
			event.platform,
		);
	}
}

@Injectable()
@EventsHandler(VerifyContactEvent)
export class VerifyContactHandler implements IEventHandler<VerifyContactEvent> {
	constructor(private readonly auditLogService: AuditLogService) {}

	handle(event: VerifyContactEvent) {
		this.auditLogService.logUserManagement(event.status, 0, {
			context: { 
				contact: event.contact,
			},
		});
	}
}
