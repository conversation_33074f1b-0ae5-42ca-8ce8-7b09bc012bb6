import {
	type MigrationInterface,
	type QueryRunner,
	Table,
	TableColumn,
	TableForeignKey,
} from 'typeorm';

export class CreateVipSystem1756492800000 implements MigrationInterface {
	public async up(queryRunner: QueryRunner): Promise<void> {
		// Create vip_tier table
		await queryRunner.createTable(
			new Table({
				name: 'vip_tier',
				columns: [
					{ name: 'id', type: 'int', isPrimary: true, isGenerated: true, generationStrategy: 'increment' },
					{ name: 'tier_name', type: 'varchar', length: '255', isUnique: true },
					{ name: 'description', type: 'text', isNullable: true },
					{ name: 'value', type: 'decimal', precision: 15, scale: 2 },
					{ name: 'benefits', type: 'text', isNullable: true }, // New column to store benefit IDs
					{ name: 'created_at', type: 'timestamp', default: 'CURRENT_TIMESTAMP' },
					{ name: 'updated_at', type: 'timestamp', default: 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' },
				],
			}),
			true,
		);

		// Create vip_benefit table (renamed from benefit)
		await queryRunner.createTable(
			new Table({
				name: 'vip_benefit',
				columns: [
					{ name: 'id', type: 'int', isPrimary: true, isGenerated: true, generationStrategy: 'increment' },
					{ name: 'name', type: 'varchar', length: '255' },
					{ name: 'description', type: 'text', isNullable: true },
					{ name: 'benefit_type', type: 'varchar', length: '255' },
					{ name: 'created_at', type: 'timestamp', default: 'CURRENT_TIMESTAMP' },
					{ name: 'updated_at', type: 'timestamp', default: 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' },
				],
			}),
			true,
		);

		// Add columns to user_account
		await queryRunner.addColumns('user_account', [
			new TableColumn({ name: 'total_recharge', type: 'decimal', precision: 15, scale: 2, default: 0 }),
			new TableColumn({ name: 'total_consume', type: 'decimal', precision: 15, scale: 2, default: 0 }),
			new TableColumn({ name: 'vip_tier_id', type: 'int', isNullable: true }),
		]);

		await queryRunner.createForeignKey(
			'user_account',
			new TableForeignKey({
				columnNames: ['vip_tier_id'],
				referencedTableName: 'vip_tier',
				referencedColumnNames: ['id'],
				onDelete: 'SET NULL',
			}),
		);

		// ----------------------
		// Seed data
		// ----------------------

		// vip_tier
		await queryRunner.query(`
			INSERT INTO vip_tier (tier_name, description, value, benefits) VALUES
			('Đồng', 'Cấp độ cơ bản, không có đặc quyền đặc biệt.', 0, '[]'),
			('Bạc', 'Cấp độ khởi đầu với các đặc quyền cơ bản.', 1000, '[1,2]'),
			('Vàng', 'Đặc quyền nâng cao so với cấp độ Bạc.', 10000, '[1,2,3,4]'),
			('Bạch Kim', 'Đặc quyền cao cấp với hỗ trợ ưu tiên.', 30000, '[1,2,3,4,5]'),
			('Kim Cương', 'Đặc quyền VIP toàn diện với hỗ trợ trực tiếp.', 50000, '[1,2,3,4,5,6,7]'),
			('Huyền Thoại', 'Cấp độ cao nhất với đầy đủ đặc quyền và hỗ trợ độc quyền.', 100000, '[1,2,3,4,5,6,7,8]');
		`);

		// vip_benefit (renamed from benefit)
		await queryRunner.query(`
			INSERT INTO vip_benefit (name, description, benefit_type) VALUES
			('Ưu tiên CSKH', 'Ưu tiên chăm sóc, hỗ trợ khách hàng qua các kênh (Fanpage + Zalo)', 'customer_support'),
			('Giftcode sự kiện', 'Nhận Giftcode sự kiện fanpage mà không cần tham gia', 'event_giftcode'),
			('Code trải nghiệm', 'Được mời tặng code khi tham gia sản phẩm mới', 'trial_code'),
			('Xử lý ưu tiên', 'Được hỗ trợ ưu tiên (Ưu tiên xử lý trước khi gặp lỗi)', 'priority_handling'),
			('Giải quyết tranh chấp', 'Hỗ trợ giải quyết tranh chấp tài khoản nhanh chóng', 'dispute_resolution'),
			('Hỗ trợ 1:1', 'Hỗ trợ nhanh các vấn đề 1:1', 'personal_support'),
			('Góp ý trực tiếp', 'Góp ý trực tiếp với đội ngũ phát triển sản phẩm', 'direct_feedback'),
			('Khôi phục tài khoản', 'Được xem xét hỗ trợ nhân vật khi bị hack (trong khả năng có thể, 1 lần duy nhất)', 'account_recovery');
		`);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		// Drop foreign key
		const table = await queryRunner.getTable('user_account');
		const foreignKey = table?.foreignKeys.find(
			(fk) => fk.columnNames.indexOf('vip_tier_id') !== -1,
		);
		if (foreignKey) {
			await queryRunner.dropForeignKey('user_account', foreignKey);
		}

		// Drop added columns (check exist trước)
		const columnsToDrop = ['total_recharge', 'total_consume', 'vip_tier_id'];
		for (const col of columnsToDrop) {
			const hasColumn = table?.findColumnByName(col);
			if (hasColumn) {
				await queryRunner.dropColumn('user_account', col);
			}
		}

		// Drop tables if exists
		await queryRunner.dropTable('vip_benefit', true); // true = ifExists
		await queryRunner.dropTable('vip_tier', true);
	}

}