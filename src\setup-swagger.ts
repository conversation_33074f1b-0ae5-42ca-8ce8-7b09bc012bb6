import type { INestApplication } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import basicAuth from 'express-basic-auth';

export function setupSwagger(app: INestApplication): void {
	app.use(
		['/documentation'],
		basicAuth({
			challenge: true,
			// this is the username and password used to authenticate
			users: { fsadmin: process.env.SWAGGER_PASSWORD || '96mB8XjbFZ5eG6t' },
		}),
	);

	const documentBuilder = new DocumentBuilder()
		.setTitle('API')
		.setDescription(
			`### REST

Routes is following REST standard (Richardson level 3)`,
		)
		.addBearerAuth();

	if (process.env.API_VERSION) {
		documentBuilder.setVersion(process.env.API_VERSION);
	}

	const document = SwaggerModule.createDocument(app, documentBuilder.build());
	SwaggerModule.setup('documentation', app, document, {
		swaggerOptions: {
			persistAuthorization: true,
		},
	});

	console.info(
		`Documentation: http://localhost:${process.env.PORT}/documentation`,
	);
}
