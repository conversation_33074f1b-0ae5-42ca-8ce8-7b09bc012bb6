// src/modules/audit-log/entities/audit-log.entity.ts
import {
	Column,
	CreateDateColumn,
	Entity,
	Index,
	JoinColumn,
	ManyToOne,
	PrimaryGeneratedColumn,
} from 'typeorm';

import { AuditLogCategory, AuditLogLevel } from '../../../constants/audit-log';
import { Platform } from '../../../constants/platform';


@Entity('audit_log')
@Index(['userId', 'action'])
@Index(['category', 'createdAt'])
@Index(['level', 'createdAt'])
@Index(['createdAt'])
// Adding composite indexes for common query patterns
@Index(['userId', 'category', 'createdAt'])
@Index(['userId', 'success', 'createdAt'])
@Index(['category', 'level', 'createdAt'])
export class AuditLogEntity {
	@PrimaryGeneratedColumn({ type: 'bigint' })
	id!: number;

	@Column({ type: 'bigint', nullable: true, name: 'user_id' })
	@Index()
	userId?: number;

	@Column({ type: 'varchar' })
	@Index()
	action!: string;

	@Column({
		type: 'enum',
		enum: AuditLogCategory,
		default: AuditLogCategory.SYSTEM,
	})
	@Index()
	category!: AuditLogCategory;

	@Column({
		type: 'enum',
		enum: AuditLogLevel,
		default: AuditLogLevel.INFO,
	})
	level!: AuditLogLevel;

	@Column('jsonb', { nullable: true })
	context?: Record<string, unknown>;

	@Column('jsonb', { nullable: true })
	metadata?: {
		ip?: string;
		userAgent?: string;
		sessionId?: string;
		requestId?: string;
		duration?: number;
		httpMethod?: string;
		endpoint?: string;
		statusCode?: number;
		errorCode?: string;
		errorMessage?: string;
		resourceId?: string;
		resourceType?: string;
		oldValues?: Record<string, unknown>;
		newValues?: Record<string, unknown>;
	};

	@Column({ type: 'varchar', nullable: true })
	ip?: string;

	@Column({ type: 'varchar', nullable: true })
	userAgent?: string;

	@Column({ type: 'varchar', nullable: true })
	sessionId?: string;

	@Column({ type: 'boolean', default: true })
	success!: boolean;

	@Column({ type: 'varchar', nullable: true })
	errorMessage?: string;

	@Column({
		type: 'enum',
		enum: Platform,
		name: 'platform',
		nullable: true,
	})
	platform?: Platform;

	@CreateDateColumn()
	createdAt!: Date;

	@ManyToOne('UserAccountEntity', 'auditLogs')
	@JoinColumn({ name: 'user_id' })
	user?: any;
}
