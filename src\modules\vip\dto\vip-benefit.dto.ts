// src/modules/vip/dto/vip-benefit.dto.ts
import { PartialType } from '@nestjs/mapped-types';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class VipBenefitDto {
	@ApiProperty({
		description: 'Benefit ID',
		example: 1,
	})
	@IsString()
	id!: number;

	@ApiProperty({
		description: 'Name of the benefit',
		example: 'Exclusive Access',
	})
	@IsString()
	name!: string;

	@ApiPropertyOptional({
		description: 'Description of the benefit',
		example: 'Access to exclusive content and features',
	})
	@IsOptional()
	@IsString()
	description?: string;

	@ApiPropertyOptional({
		description: 'Type of the benefit',
		example: 'customer_support',
	})
	@IsString()
	benefitType?: string;
}

export class UpdateVipBenefitDto extends PartialType(VipBenefitDto) {}
