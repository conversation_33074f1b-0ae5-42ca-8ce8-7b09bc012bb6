import { ApiProperty } from '@nestjs/swagger';

export class RoleDto {
	@ApiProperty({ example: 'r123' })
	roleId!: string;

	@ApiProperty({ example: 'Warrior' })
	roleName!: string;

	@ApiProperty({ example: 50 })
	level!: number;
}

export class ServerDto {
	@ApiProperty({ example: 's1' })
	serverId!: string;

	@ApiProperty({ example: 'Server 1' })
	serverName!: string;

	@ApiProperty({ type: [RoleDto] })
	roles!: RoleDto[];
}

export class ServersRolesResponseDto {
	@ApiProperty({ type: [ServerDto] })
	servers!: ServerDto[];
}
