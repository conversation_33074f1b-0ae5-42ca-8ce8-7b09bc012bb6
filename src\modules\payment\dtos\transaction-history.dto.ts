import { PaymentMethod, PaymentStatus } from '@constants/payment';
import {
	DateField,
	EnumField,
	NumberField,
	StringField,
	UUIDField,
} from '@decorators/field.decorators';

export class TransactionHistoryDto {
	@UUIDField()
	txId?: string;

	@StringField()
	gameKey?: string;

	@StringField()
	packKey?: string;

	@NumberField()
	amount?: number;

	@StringField()
	currency?: string;

	@EnumField(() => PaymentMethod)
	paymentMethod?: PaymentMethod;

	@EnumField(() => PaymentStatus)
	status?: PaymentStatus;

	@StringField()
	note?: string;

	@DateField()
	createdAt?: Date;

	constructor(partial: Partial<TransactionHistoryDto>) {
		Object.assign(this, partial);
	}
}
