import 'reflect-metadata';
import './boilerplate.polyfill';

import {
	ClassSerializerInterceptor,
	HttpStatus,
	UnprocessableEntityException,
	ValidationPipe,
} from '@nestjs/common';
import { NestFactory, Reflector } from '@nestjs/core';
import type { NestExpressApplication } from '@nestjs/platform-express';
import { ExpressAdapter } from '@nestjs/platform-express';
import { AppPaymentModule } from 'app-payment.module.ts';
import compression from 'compression';
import helmet from 'helmet';
import morgan from 'morgan';
import { initializeTransactionalContext } from 'typeorm-transactional';

import { HttpExceptionFilter } from './filters/http-exception.filter.ts';
import { loadEnvFromInfisical } from './infisical-loader.ts';
import { ResponseInterceptor } from './interceptors/response-interceptor.service.ts';
import { setupSwagger } from './setup-swagger.ts';
import { ApiConfigService } from './shared/services/api-config.service.ts';
import { SharedModule } from './shared/shared.module.ts';

export async function bootstrap(): Promise<NestExpressApplication> {
	initializeTransactionalContext();

	// Setup Infisical
	await loadEnvFromInfisical();

	const app = await NestFactory.create<NestExpressApplication>(
		AppPaymentModule,
		new ExpressAdapter(),
		{
			cors: {
				origin: process.env.CORS_ORIGINS?.split(',') || [
					'http://localhost:4002',
				],
				methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
				credentials: true,
			},
		},
	);
	app.enable('trust proxy'); // only if you're behind a reverse proxy (Heroku, Bluemix, AWS ELB, Nginx, etc)
	app.use(helmet());
	app.use(compression());
	app.use(morgan('combined'));
	app.enableVersioning();

	const reflector = app.get(Reflector);

	app.useGlobalFilters(new HttpExceptionFilter());

	app.useGlobalInterceptors(
		new ResponseInterceptor(reflector),
		new ClassSerializerInterceptor(reflector),
	);

	app.useGlobalPipes(
		new ValidationPipe({
			whitelist: false,
			errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
			transform: true,
			dismissDefaultMessages: true,
			forbidNonWhitelisted: true,
			exceptionFactory: (errors) => new UnprocessableEntityException(errors),
		}),
	);

	const configService = app.select(SharedModule).get(ApiConfigService);

	if (configService.documentationEnabled) {
		setupSwagger(app);
	}

	// Starts listening for shutdown hooks
	if (!configService.isDevelopment) {
		app.enableShutdownHooks();
	}

	const port = configService.appConfig.portPayment;

	await app.listen(port);
	console.info(`server running on ${await app.getUrl()}`);

	return app;
}

export const viteNodeApp = await bootstrap();
