import { QuickplayModule } from '@modules/quickplay/quickplay.module';
import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { CqrsModule } from '@nestjs/cqrs';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ApiConfigService } from '@shared/services/api-config.service';

import { UserModule } from '../user/user.module';
import { AuthService } from './auth.service';
import { JwtStrategy } from './jwt.strategy';

@Module({
	imports: [
		UserModule,
		QuickplayModule,
		PassportModule.register({ defaultStrategy: 'jwt' }),
		JwtModule.registerAsync({
			useFactory: (configService: ApiConfigService) => ({
				privateKey: configService.authConfig.privateKey,
				publicKey: configService.authConfig.publicKey,
				signOptions: {
					algorithm: 'RS256',
					expiresIn: configService.getNumber('JWT_EXPIRATION_TIME'),
				},
				verifyOptions: {
					algorithms: ['RS256'],
				},
			}),
			inject: [ApiConfigService],
		}),
		PassportModule,
		HttpModule,
		ConfigModule,
		CqrsModule,
	],
	providers: [
		AuthService,
		JwtStrategy,
	],
	exports: [JwtModule, AuthService],
})
export class AuthPaymentModule {}
