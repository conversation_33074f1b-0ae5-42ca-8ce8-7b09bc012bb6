import { AbstractDto } from '@common/dto/abstract.dto';
import {
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	StringField,
} from '@decorators/field.decorators';

import type { GameEntity } from '../game.entity';

export class GameDto extends AbstractDto {
	@NumberField()
	gameId!: number;

	@StringField()
	gameKey!: string;

	@StringField()
	gameName!: string;

	@StringField()
	apiEndpoint!: string;

	@BooleanField()
	isApiActive!: boolean;

	@DateField()
	declare createdAt: Date;

	@DateField()
	declare updatedAt: Date;

	constructor(game: GameEntity) {
		super(game);
		this.gameId = game.gameId;
		this.gameKey = game.gameKey;
		this.gameName = game.gameName;
		this.createdAt = game.createdAt;
		this.updatedAt = game.updatedAt;
	}
}
