import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class OnepayPaymentRequestDto {
	@ApiProperty({
		description: 'Version module cổng thanh toán, mặc định là 2',
		example: '2',
	})
	@IsString()
	vpc_Version!: string;

	@ApiProperty({
		description: 'Đơn vị tiền tệ, mặc định là VND được cấu hình theo tài khoản kỹ thuật vpc_Merchant. Đơn vị cần lấy đúng currency của merchant ID được cấp (khi tích hợp OP sẽ note là ID sẽ dùng currency nào)',
		example: 'VND',
	})
	@IsString()
	vpc_Currency!: string;

	@ApiProperty({
		description: 'Lệnh thanh toán, mặc định là pay',
		example: 'pay',
	})
	@IsString()
	vpc_Command!: string;

	@ApiProperty({
		description: 'T<PERSON><PERSON> khoản kỹ thuật do OnePay cung cấp cho đơn vị',
		example: '6BEB2546',
	})
	@IsString()
	vpc_AccessCode!: string;

	@ApiProperty({
		description: 'Tài khoản kỹ thuật do OnePay cung cấp cho đơn vị',
		example: 'TESTDEFAULT',
	})
	@IsString()
	vpc_Merchant!: string;

	@ApiProperty({
		description: 'Ngôn ngữ hiển thị giao diện thanh toán, mặc định là vn',
		example: 'vn',
	})
	@IsString()
	vpc_Locale!: string;

	@ApiProperty({
		description: 'URL trả về khi giao dịch thành công',
		example: 'https://example.com/success',
	})
	@IsString()
	vpc_ReturnURL!: string;

	@ApiProperty({
		description: 'Mã giao dịch, là mã duy nhất được tạo cho mỗi giao dịch, không chứa tiếng Việt có dấu và các ký tự quá đặc biệt: "(", "/", "&", "?", ")". Nên sử dụng dấu gạch dưới “_” và dấu gạch ngang “-” trong chuỗi',
		example: '123456',
	})
	@IsString()
	vpc_MerchTxnRef!: string;

	@ApiProperty({
		description: 'Mã đơn hàng, thông tin đơn hàng,... Không chứa tiếng Việt có dấu và các ký quá tự đặc biệt. Có thể dùng dấu cách, dấu gạch dưới “_” trong chuỗi',
		example: 'Ma_Don_Hang_2',
	})
	@IsString()
	vpc_OrderInfo!: string;

	@ApiProperty({
		description: 'Số tiền thanh toán, đơn vị tiền tệ theo vpc_Currency, Số tiền thanh toán, không có dấu ngăn cách đơn vị nghìn, trăm nghìn, triệu',
		example: '10000',
	})
	@IsString()
	vpc_Amount!: string;

	@ApiProperty({
		description: 'Địa chỉ IP khách hàng thực hiện thanh toán. Không được đặt cố định 1 IP.',
		example: '127.0.0.1',
	})
	@IsString()
	vpc_TicketNo!: string;

	@ApiProperty({
		description: 'Danh sách các loại thẻ được phép thanh toán, mặc định là all',
		example: 'all',
	})
	@IsString()
	@IsOptional()
	vpc_CardList?: string;

	@ApiProperty({
		description: 'Số điện thoại của khách hàng',
		example: '0987654321',
	})
	@IsString()
	@IsOptional()
	vpc_Customer_Phone?: string;

	@ApiProperty({
		description: 'Email của khách hàng',
		example: '<EMAIL>',
	})
	@IsString()
	@IsOptional()
	vpc_Customer_Email?: string;

	@ApiProperty({
		description: 'Id khách hàng, mã duy nhất của khách hàng trên hệ thống đơn vị. Đơn vị không được truyền giá trị mặc định sang OnePay.',
		example: '123456',
	})
	@IsString()
	@IsOptional()
	vpc_Customer_Id?: string;

	@ApiProperty({
		description: 'URL nhận thông báo trạng thái giao dịch IPN',
		example: 'https://example.com/callback',
	})
	@IsString()
	@IsOptional()
	vpc_CallbackURL?: string;

	@ApiProperty({
		description: 'Mã hash an toàn',
		example: 'hash',
	})
	@IsString()
	@IsOptional()
	vpc_SecureHash?: string;
}
