import type { MigrationInterface, QueryRunner } from 'typeorm';

export class DropPaymentTransaction1757002275295 implements MigrationInterface {
	name = 'DropPaymentTransaction1757002275295';

	public async up(queryRunner: QueryRunner): Promise<void> {
		// Drop foreign key constraints first
		await queryRunner.query(
			'ALTER TABLE IF EXISTS "payment_transaction" DROP CONSTRAINT IF EXISTS "FK_0362c4a86732d07164a772f8292"',
		);
		await queryRunner.query(
			'ALTER TABLE IF EXISTS "payment_transaction" DROP CONSTRAINT IF EXISTS "FK_6f438a44a054f881f3e8813174f"',
		);
		await queryRunner.query(
			'ALTER TABLE IF EXISTS "payment_transaction" DROP CONSTRAINT IF EXISTS "FK_payment_transaction_user_account"',
		);
		await queryRunner.query(
			'ALTER TABLE IF EXISTS "payment_transaction" DROP CONSTRAINT IF EXISTS "FK_payment_transaction_game"',
		);

		// Drop indices (non-unique indices don't need CASCADE)
		await queryRunner.query('DROP INDEX IF EXISTS "public"."idx_payment_game"');
		await queryRunner.query('DROP INDEX IF EXISTS "public"."idx_payment_status"');
		await queryRunner.query('DROP INDEX IF EXISTS "public"."idx_payment_user"');
		
		// Drop unique indices/constraints
		await queryRunner.query('DROP INDEX IF EXISTS "public"."payment_transaction_order_id_key"');
		// Comment: Primary key index sẽ tự động drop khi drop table, không cần drop riêng
		// await queryRunner.query('DROP INDEX IF EXISTS "public"."payment_transaction_pkey"');

		// Drop table last (will automatically drop primary key and any remaining constraints)
		await queryRunner.query('DROP TABLE IF EXISTS "payment_transaction"');
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		// Recreate the payment transaction table
		await queryRunner.query(`
			CREATE TABLE "payment_transaction" (
				"created_at" TIMESTAMP NOT NULL DEFAULT now(),
				"updated_at" TIMESTAMP NOT NULL DEFAULT now(),
				"tx_id" BIGSERIAL NOT NULL,
				"user_id" bigint NOT NULL,
				"game_id" integer NOT NULL,
				"order_id" character varying(100) NOT NULL,
				"amount" numeric(10,2) NOT NULL,
				"currency" character varying(10) NOT NULL,
				"payment_method" character varying(30) NOT NULL,
				"status" character varying(30) NOT NULL DEFAULT 'pending',
				"note" character varying(100) NOT NULL,
				CONSTRAINT "UQ_91163b302301738c73b0b917a1c" UNIQUE ("order_id"),
				CONSTRAINT "PK_43d7c92466f8b34e443d9bc0a03" PRIMARY KEY ("tx_id")
			)
		`);

		// Recreate indices
		await queryRunner.query(
			'CREATE INDEX "idx_payment_user" ON "payment_transaction" ("user_id")',
		);
		await queryRunner.query(
			'CREATE INDEX "idx_payment_status" ON "payment_transaction" ("status")',
		);
		await queryRunner.query(
			'CREATE INDEX "idx_payment_game" ON "payment_transaction" ("game_id")',
		);
	}
}