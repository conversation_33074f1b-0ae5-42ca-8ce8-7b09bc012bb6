import { SysConfigModule } from '@modules/sys-config/sys-config.module';
import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { CqrsModule } from '@nestjs/cqrs';

import { AuditLogModule } from '../audit-log/audit-log.module';
import { ZaloOAuthController } from './zalo-oauth.controller';
import { ZaloCron } from './zalo-oauth.cron';
import { ZaloOAuthService } from './zalo-oauth.service';

@Module({
	imports: [
		HttpModule,
		ConfigModule,
		SysConfigModule,
		AuditLogModule,
		CqrsModule,
	],
	controllers: [ZaloOAuthController],
	providers: [ZaloOAuthService, ZaloCron],
	exports: [ZaloOAuthService],
})
export class ZaloOAuthModule {}
