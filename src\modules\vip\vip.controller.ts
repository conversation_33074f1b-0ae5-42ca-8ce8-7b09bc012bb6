import { RoleType } from '@constants/role-type';
import { ApiCommonResponse } from '@decorators/api-common-response.decorator';
import { Auth } from '@decorators/http.decorators';
import {
	Body,
	Controller,
	Get,
	Post,
} from '@nestjs/common';
import {
	ApiBadRequestResponse,
	ApiBody,
	ApiNotFoundResponse,
	ApiOperation,
	ApiTags,
} from '@nestjs/swagger';

import { RechargeDto } from './dto/recharge.dto';
import { VipTierDto } from './dto/vip-tier.dto';
import { VipService } from './vip.service';


@Controller('vip')
@ApiTags('VIP')
export class VipController {
	constructor(private readonly vipService: VipService) {}

	// VIP Tier endpoints
	@Get('tiers')
	@ApiOperation({ summary: 'Get all VIP tiers' })
	@ApiCommonResponse({
		description: 'List of all VIP tiers retrieved successfully',
		type: [VipTierDto],
	})
	async findAllVipTiers() {
		return await this.vipService.findAllVipTiers();
	}

	// @Get('tiers/:id')
	// @ApiOperation({ summary: 'Get VIP tier by ID' })
	// @ApiCommonResponse({
	// 	description: 'VIP tier retrieved successfully',
	// 	type: VipTierEntity,
	// })
	// @ApiNotFoundResponse({
	// 	description: 'VIP tier not found',
	// 	example: {
	// 		statusCode: 404,
	// 		message: 'VIP Tier with ID 999 not found',
	// 		error: 'Not Found',
	// 	},
	// })
	// async findVipTierById(@Param('id', ParseIntPipe) id: number) {
	// 	return await this.vipService.findVipTierById(id);
	// }

	// @Post('tiers')
	// @ApiOperation({ summary: 'Create a new VIP tier' })
	// @ApiBody({ type: CreateVipTierDto })
	// @ApiCreatedResponse({
	// 	description: 'VIP tier created successfully',
	// 	type: VipTierEntity,
	// 	example: {
	// 		id: 3,
	// 		tier_name: 'Platinum',
	// 		description: 'Highest VIP tier with all benefits',
	// 		value: 5000,
	// 		benefits: [
	// 			{
	// 				id: 1,
	// 				name: 'Basic Access',
	// 				description: 'Access to basic features',
	// 			},
	// 			{
	// 				id: 2,
	// 				name: 'Priority Support',
	// 				description: 'Faster customer support',
	// 			},
	// 			{
	// 				id: 3,
	// 				name: 'Exclusive Content',
	// 				description: 'Access to premium content',
	// 			},
	// 			{
	// 				id: 4,
	// 				name: 'VIP Events',
	// 				description: 'Access to exclusive events',
	// 			},
	// 		],
	// 	},
	// })
	// @ApiBadRequestResponse({
	// 	description: 'Invalid input data or validation failed',
	// 	example: {
	// 		statusCode: 400,
	// 		message: [
	// 			'tier_name must be a string',
	// 			'value must be a number',
	// 		],
	// 		error: 'Bad Request',
	// 	},
	// })
	// async createVipTier(@Body() createVipTierDto: CreateVipTierDto) {
	// 	return await this.vipService.createVipTier(createVipTierDto);
	// }

	// @Put('tiers/:id')
	// @ApiOperation({ summary: 'Update a VIP tier' })
	// @ApiBody({ type: UpdateVipTierDto })
	// @ApiCommonResponse({
	// 	description: 'VIP tier updated successfully',
	// 	type: VipTierEntity,
	// })
	// @ApiNotFoundResponse({
	// 	description: 'VIP tier not found',
	// 	example: {
	// 		statusCode: 404,
	// 		message: 'VIP Tier with ID 999 not found',
	// 		error: 'Not Found',
	// 	},
	// })
	// @ApiBadRequestResponse({
	// 	description: 'Invalid input data or validation failed',
	// 	example: {
	// 		statusCode: 400,
	// 		message: [
	// 			'tier_name must be a string',
	// 			'value must be a number',
	// 		],
	// 		error: 'Bad Request',
	// 	},
	// })
	// async updateVipTier(
	// 	@Param('id', ParseIntPipe) id: number,
	// 	@Body() updateVipTierDto: UpdateVipTierDto,
	// ) {
	// 	return await this.vipService.updateVipTier(id, updateVipTierDto);
	// }

	// @Delete('tiers/:id')
	// @ApiOperation({ summary: 'Delete a VIP tier' })
	// @ApiCommonResponse({
	// 	description: 'VIP tier deleted successfully',
	// })
	// @ApiNotFoundResponse({
	// 	description: 'VIP tier not found',
	// 	example: {
	// 		statusCode: 404,
	// 		message: 'VIP Tier with ID 999 not found',
	// 		error: 'Not Found',
	// 	},
	// })
	// async deleteVipTier(@Param('id', ParseIntPipe) id: number) {
	// 	await this.vipService.deleteVipTier(id);
	// 	return { message: 'VIP Tier deleted successfully' };
	// }

	// // VIP Benefit endpoints
	// @Get('vip-benefits')
	// @ApiOperation({ summary: 'Get all VIP benefits' })
	// @ApiCommonResponse({
	// 	description: 'List of all VIP benefits retrieved successfully',
	// 	type: [Object],
	// })
	// async findAllVipBenefits() {
	// 	return await this.vipService.findAllVipBenefits();
	// }

	// @Get('vip-benefits/:id')
	// @ApiOperation({ summary: 'Get VIP benefit by ID' })
	// @ApiCommonResponse({
	// 	description: 'VIP benefit retrieved successfully',
	// 	type: Object,
	// })
	// @ApiNotFoundResponse({
	// 	description: 'VIP benefit not found',
	// 	example: {
	// 		statusCode: 404,
	// 		message: 'VIP benefit with ID 999 not found',
	// 		error: 'Not Found',
	// 	},
	// })
	// async findVipBenefitById(@Param('id', ParseIntPipe) id: number) {
	// 	return await this.vipService.findVipBenefitById(id);
	// }

	// @Post('vip-benefits')
	// @ApiOperation({ summary: 'Create a new VIP benefit' })
	// @ApiBody({ type: CreateVipBenefitDto })
	// @ApiCreatedResponse({
	// 	description: 'VIP benefit created successfully',
	// 	type: Object,
	// 	example: {
	// 		id: 5,
	// 		name: 'New VIP Benefit',
	// 		description: 'Newly created VIP benefit',
	// 	},
	// })
	// @ApiBadRequestResponse({
	// 	description: 'Invalid input data or validation failed',
	// 	example: {
	// 		statusCode: 400,
	// 		message: [
	// 			'name must be a string',
	// 			'description must be a string',
	// 		],
	// 		error: 'Bad Request',
	// 	},
	// })
	// async createVipBenefit(@Body() createVipBenefitDto: CreateVipBenefitDto) {
	// 	return await this.vipService.createVipBenefit(createVipBenefitDto);
	// }

	// @Put('vip-benefits/:id')
	// @ApiOperation({ summary: 'Update a VIP benefit' })
	// @ApiBody({ type: UpdateVipBenefitDto })
	// @ApiCommonResponse({
	// 	description: 'VIP benefit updated successfully',
	// 	type: Object,
	// })
	// @ApiNotFoundResponse({
	// 	description: 'VIP benefit not found',
	// 	example: {
	// 		statusCode: 404,
	// 		message: 'VIP benefit with ID 999 not found',
	// 		error: 'Not Found',
	// 	},
	// })
	// @ApiBadRequestResponse({
	// 	description: 'Invalid input data or validation failed',
	// 	example: {
	// 		statusCode: 400,
	// 		message: [
	// 			'name must be a string',
	// 			'description must be a string',
	// 		],
	// 		error: 'Bad Request',
	// 	},
	// })
	// async updateVipBenefit(
	// 	@Param('id', ParseIntPipe) id: number,
	// 	@Body() updateVipBenefitDto: UpdateVipBenefitDto,
	// ) {
	// 	return await this.vipService.updateVipBenefit(id, updateVipBenefitDto);
	// }

	// @Delete('vip-benefits/:id')
	// @ApiOperation({ summary: 'Delete a VIP benefit' })
	// @ApiCommonResponse({
	// 	description: 'VIP benefit deleted successfully',
	// })
	// @ApiNotFoundResponse({
	// 	description: 'VIP benefit not found',
	// 	example: {
	// 		statusCode: 404,
	// 		message: 'VIP benefit with ID 999 not found',
	// 		error: 'Not Found',
	// 	},
	// })
	// async deleteVipBenefit(@Param('id', ParseIntPipe) id: number) {
	// 	await this.vipService.deleteVipBenefit(id);
	// 	return { message: 'VIP benefit deleted successfully' };
	// }

	@Post('recharge')
	@ApiBody({ type: RechargeDto })
	@Auth([RoleType.ADMIN])
	@ApiOperation({ summary: 'Recharge user VIP balance (Admin Only)' })
	@ApiCommonResponse({
		description: 'User VIP balance updated successfully',
		type: Object,
	})
	@ApiNotFoundResponse({
		description: 'User not found',
		example: {
			statusCode: 404,
			message: 'User with ID 999 not found',
			error: 'Not Found',
		},
	})
	@ApiBadRequestResponse({
		description: 'Invalid input data or validation failed',
		example: {
			statusCode: 400,
			message: [
				'amount must be a positive number',
				'amount must be greater than 0',
			],
			error: 'Bad Request',
		},
	})
	async recharge(
		@Body() rechargeDto: RechargeDto,
	) {
		return await this.vipService.updateUserRecharge(rechargeDto.userId, rechargeDto.amount);
	}
}
