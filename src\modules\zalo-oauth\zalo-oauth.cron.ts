import { AuditLogCategory } from '@constants/audit-log';
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';

import { AuditLogService } from '../audit-log/audit-log.service';
import { ZaloOAuthService } from './zalo-oauth.service';

@Injectable()
export class ZaloCron {
	private readonly logger = new Logger(ZaloCron.name);

	constructor(
		private readonly zaloService: ZaloOAuthService,
		private readonly auditLogService: AuditLogService,
	) {}

	// Cronjob chạy mỗi ngày lúc 0h00
	@Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
	async handleDailyRefresh() {
		this.logger.log('Running daily refreshAccessToken job...');
		
		try {
			await this.zaloService.refreshAccessToken();
			this.logger.log('Daily refreshAccessToken completed successfully');
			
			// Log successful token refresh to audit log
			await this.auditLogService.logAction({
				action: 'zalo_token_refresh_success',
				category: AuditLogCategory.SYSTEM,
				context: {
					description: 'Daily Zalo access token refresh completed successfully',
					cronJob: 'EVERY_DAY_AT_MIDNIGHT',
				},
			});
			
		} catch (err: unknown) {
			const error = err as Error;
			this.logger.error('Daily refreshAccessToken failed', error.stack);
			
			// Log failed token refresh to audit log
			await this.auditLogService.logAction({
				action: 'zalo_token_refresh_failed',
				category: AuditLogCategory.SYSTEM,
				success: false,
				errorMessage: error.message,
				context: {
					description: 'Daily Zalo access token refresh failed',
					cronJob: 'EVERY_DAY_AT_MIDNIGHT',
					error: error.stack,
				},
			});
		}
	}
}