import { HelpersModule } from '@common/helpers/helpers.module';
import { AuditLogModule } from '@modules/audit-log/audit-log.module';
import { QuickplayModule } from '@modules/quickplay/quickplay.module';
import { SysConfigModule } from '@modules/sys-config/sys-config.module';
import { HttpModule } from '@nestjs/axios';
import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { CqrsModule } from '@nestjs/cqrs';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ApiConfigService } from '@shared/services/api-config.service';
import { OtpService } from '@shared/services/otp.service';
import { SecurityLoggerService } from '@shared/services/security-logger.service';

import { UserModule } from '../user/user.module';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { FacebookStrategy } from './facebook.strategy';
import { GoogleStrategy } from './google.strategy';
import { JwtStrategy } from './jwt.strategy';
import { PublicStrategy } from './public.strategy';

@Module({
	imports: [
		forwardRef(() => UserModule),
		AuditLogModule,
		QuickplayModule,
		PassportModule.register({ defaultStrategy: 'jwt' }),
		JwtModule.registerAsync({
			useFactory: (configService: ApiConfigService) => ({
				privateKey: configService.authConfig.privateKey,
				publicKey: configService.authConfig.publicKey,
				signOptions: {
					algorithm: 'RS256',
					expiresIn: configService.getNumber('JWT_EXPIRATION_TIME'),
				},
				verifyOptions: {
					algorithms: ['RS256'],
				},
			}),
			inject: [ApiConfigService],
		}),
		PassportModule,
		HttpModule,
		SysConfigModule,
		HelpersModule,
		ConfigModule,
		CqrsModule,
	],
	controllers: [AuthController],
	providers: [
		AuthService,
		JwtStrategy,
		PublicStrategy,
		GoogleStrategy,
		FacebookStrategy,
		SecurityLoggerService,
		OtpService,
	],
	exports: [JwtModule, AuthService],
})
export class AuthModule {}
