import { type MigrationInterface, type QueryRunner, TableColumn } from 'typeorm';

export class AddIdRelatedFieldsToUserProfileTables1755236351472 implements MigrationInterface {
	public async up(queryRunner: QueryRunner): Promise<void> {
		// Add columns to user_profile table
		const table = await queryRunner.getTable('user_profile');
		if (table && !table.findColumnByName('id_issue_date')) {
			await queryRunner.addColumn(
				'user_profile',
				new TableColumn({
					name: 'id_issue_date',
					type: 'date',
					isNullable: true,
				}),
			);
		}
		if (table && !table.findColumnByName('id_front_url')) {
			await queryRunner.addColumn(
				'user_profile',
				new TableColumn({
					name: 'id_front_url',
					type: 'varchar',
					length: '500',
					isNullable: true,
				}),
			);
		}
		if (table && !table.findColumnByName('id_back_url')) {
			await queryRunner.addColumn(
				'user_profile',
				new TableColumn({
					name: 'id_back_url',
					type: 'varchar',
					length: '500',
					isNullable: true,
				}),
			);
		}
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		// Remove columns from user_profile
		const table = await queryRunner.getTable('user_profile');
		if (table && table.findColumnByName('id_issue_date')) {
			await queryRunner.dropColumn('user_profile', 'id_issue_date');
		}
		if (table && table.findColumnByName('id_front_url')) {
			await queryRunner.dropColumn('user_profile', 'id_front_url');
		}
		if (table && table.findColumnByName('id_back_url')) {
			await queryRunner.dropColumn('user_profile', 'id_back_url');
		}
	}
}