import { type MigrationInterface, type QueryRunner, Table } from 'typeorm';

export class CreatePaymentMethodTable1757002275294 implements MigrationInterface {
	name = 'CreatePaymentMethodTable1757002275294';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.createTable(
			new Table({
				name: 'payment_method',
				columns: [
					{
						name: 'payment_method',
						type: 'varchar',
						length: '50',
						isPrimary: true,
					},
					{
						name: 'status',
						type: 'enum',
						enum: ['enabled', 'disabled'],
						default: '\'enabled\'',
					},
				],
			}),
			true,
		);

		// Seed initial methods
		await queryRunner.query(`
            INSERT INTO payment_method (payment_method, status) VALUES
            ('INTERCARD', 'enabled'),
            ('DOMESTIC', 'enabled'),
            ('MOBILEBANKING', 'enabled'),
            ('APPLEPAY', 'enabled'),
            ('GOOGLEPAY', 'enabled')
        `);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.dropTable('payment_method');
	}
}