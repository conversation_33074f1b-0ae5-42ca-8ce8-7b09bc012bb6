import { StringField } from '@decorators/field.decorators';
import { ApiProperty } from '@nestjs/swagger';

export class QuickplayUserDto {
	@StringField()
	username!: string;
}
export class QuickplayResponseDto {
	@ApiProperty({
		description: 'User account information',
		type: QuickplayUserDto,
	})
	user!: QuickplayUserDto;

	constructor(partial: Partial<QuickplayResponseDto>) {
		Object.assign(this, partial);
	}
}
