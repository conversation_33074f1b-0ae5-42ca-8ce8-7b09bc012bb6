// src/modules/audit-log/services/audit-log.service.ts
import { hash<PERSON><PERSON><PERSON>ey } from '@common/utils';
import { AuditLogCategory, AuditLogLevel } from '@constants/audit-log';
import { Platform } from '@constants/platform';
import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { RedisKeyManagerService } from '@shared/services/redis-key-manager.service';
import { type Queue } from 'bull';
import {
	InvalidDateException,
	InvalidUserIdException,
} from 'exceptions';
import { Repository } from 'typeorm';

import { ApiConfigService } from '../../shared/services/api-config.service';
import { RedisService } from '../../shared/services/redis.service';
import { CreateAuditLogDto, QueryAuditLogDto } from './dtos/audit-log.dto';
import { AuditLogEntity } from './entities/audit-log.entity';

interface AuditLogOptions {
	userId?: number;
	action: string;
	category: AuditLogCategory;
	level?: AuditLogLevel;
	context?: Record<string, unknown>;
	metadata?: Record<string, unknown>;
	ip?: string;
	userAgent?: string;
	sessionId?: string;
	requestId?: string;
	success?: boolean;
	errorMessage?: string;
	platform?: Platform; // Optional for SYSTEM category
	immediate?: boolean; // Skip queue for critical logs
}

@Injectable()
export class AuditLogService {
	private readonly logger = new Logger(AuditLogService.name);
	private readonly CACHE_PREFIX = 'audit_log';
	private readonly CACHE_TTL = 300; // 5 minutes
	private readonly enableDatabase: boolean;
	private readonly enableRedisCache: boolean;

	constructor(
		@InjectRepository(AuditLogEntity)
		private auditLogRepository: Repository<AuditLogEntity>,
		@InjectQueue('audit-log')
		private auditLogQueue: Queue,
		private redisService: RedisService,
		private readonly redisKeyManager: RedisKeyManagerService,
		private configService: ApiConfigService,
	) {
		this.enableDatabase = this.configService.auditLogDatabaseEnabled;
		this.enableRedisCache = this.configService.auditLogRedisCacheEnabled;
	}

	/**
	 * Main method to log audit events
	 */
	async logAction(options: AuditLogOptions): Promise<void> {
		try {
			// For SYSTEM category, platform is optional and can be undefined
			let platform = options.platform;
			if (options.category === AuditLogCategory.SYSTEM && !options.platform) {
				platform = undefined;
			}

			const logData: CreateAuditLogDto = {
				...options,
				platform,
				level: options.level || AuditLogLevel.INFO,
				success: options.success !== false,
			};

			if (options.immediate) {
				await this.processAndSaveLog(logData);
			} else {
				await this.auditLogQueue.add('process-log', logData, {
					attempts: 3,
					backoff: {
						type: 'exponential',
						delay: 1000,
					},
					removeOnComplete: true,
					removeOnFail: true,
				});
			}
		} catch (error) {
			this.logger.error('Failed to log audit action', (error as Error).stack);
		}
	}

	/**
	 * Process and save the log. This can be called directly or by the queue processor.
	 */
	private async processAndSaveLog(data: CreateAuditLogDto): Promise<void> {
		const sanitizedData = this.sanitizeData(data);

		if (this.enableDatabase) {
			const logEntry = this.auditLogRepository.create(sanitizedData);
			await this.auditLogRepository.save(logEntry);
		}

		if (this.enableRedisCache) {
			const cacheKey = `${this.CACHE_PREFIX}:recent:${sanitizedData.userId || 'system'}`;
			await this.redisService.lpush(cacheKey, JSON.stringify(sanitizedData));
			await this.redisService.ltrim(cacheKey, 0, 99);
			await this.redisService.expire(cacheKey, 86400);
		}
	}

	/**
	 * Convenience methods for different categories
	 */
	async logAuth(
		action: string,
		userId?: number,
		options?: Partial<AuditLogOptions>,
		platform?: Platform,
	): Promise<void> {
		return this.logAction({
			action,
			userId,
			category: AuditLogCategory.AUTHENTICATION,
			level: AuditLogLevel.INFO,
			platform,
			...options,
		});
	}

	async logPayment(
		action: string,
		userId: number,
		options?: Partial<AuditLogOptions>,
		platform?: Platform,
	): Promise<void> {
		return this.logAction({
			action,
			userId,
			category: AuditLogCategory.PAYMENT,
			level: AuditLogLevel.INFO,
			platform,
			...options,
		});
	}

	async logGame(
		action: string,
		userId: number,
		options?: Partial<AuditLogOptions>,
		platform?: Platform,
	): Promise<void> {
		return this.logAction({
			action,
			userId,
			category: AuditLogCategory.GAME,
			level: AuditLogLevel.INFO,
			platform,
			...options,
		});
	}

	async logUserManagement(
		action: string,
		userId: number,
		options?: Partial<AuditLogOptions>,
		platform?: Platform,
	): Promise<void> {
		return this.logAction({
			action,
			userId,
			category: AuditLogCategory.USER_MANAGEMENT,
			level: AuditLogLevel.INFO,
			platform,
			...options,
		});
	}

	async logSecurity(
		action: string,
		userId: number,
		options?: Partial<AuditLogOptions>,
		platform?: Platform,
	): Promise<void> {
		return this.logAction({
			action,
			userId,
			category: AuditLogCategory.SECURITY,
			level: AuditLogLevel.WARNING,
			platform,
			immediate: true, // Security events should be processed immediately
			...options,
		});
	}

	async logError(
		action: string,
		error: Error,
		options?: Partial<AuditLogOptions>,
		platform?: Platform,
	): Promise<void> {
		return this.logAction({
			action,
			category: AuditLogCategory.SYSTEM,
			level: AuditLogLevel.ERROR,
			platform,
			success: false,
			errorMessage: error.message,
			context: { stack: error.stack },
			immediate: true,
			...options,
		});
	}

	/**
   * Query audit logs with caching
   */
	async queryLogs(query: QueryAuditLogDto): Promise<{ logs: AuditLogEntity[]; total: number }> {
		// If category is provided but invalid, return empty results
		if (query.category && !Object.values(AuditLogCategory).includes(query.category)) {
			return { logs: [], total: 0 };
		}

		const cacheKey = this.buildCacheKey('query', query);

		if (this.enableRedisCache) {
			const cached = await this.redisService.get<string>(cacheKey);
			if (cached) {
				return JSON.parse(cached);
			}
		}

		const queryBuilder = this.auditLogRepository.createQueryBuilder('audit_log');

		// Build where conditions more efficiently
		const whereConditions: string[] = [];
		const whereParams: Record<string, any> = {};

		if (query.userId) {
			whereConditions.push('audit_log.userId = :userId');
			whereParams.userId = query.userId;
		}

		if (query.action) {
			// Using startsWith instead of contains for better index usage
			// If full text search is needed, consider using full-text search capabilities
			whereConditions.push('audit_log.action ILIKE :action');
			whereParams.action = `${query.action}%`; // Remove leading % for better performance
		}

		if (query.category) {
			whereConditions.push('audit_log.category = :category');
			whereParams.category = query.category;
		}

		if (query.level) {
			whereConditions.push('audit_log.level = :level');
			whereParams.level = query.level;
		}

		if (query.platform) {
			whereConditions.push('audit_log.platform = :platform');
			whereParams.platform = query.platform;
		}

		if (query.success !== undefined) {
			whereConditions.push('audit_log.success = :success');
			whereParams.success = query.success;
		}

		if (query.startDate && query.endDate) {
			whereConditions.push('audit_log.createdAt BETWEEN :startDate AND :endDate');
			whereParams.startDate = query.startDate;
			whereParams.endDate = query.endDate;
		}

		if (query.search) {
			// Use a more efficient search that can leverage indexes
			whereConditions.push(
				'(audit_log.action ILIKE :searchAction OR audit_log.errorMessage ILIKE :searchError)',
			);
			whereParams.searchAction = `%${query.search}%`;
			whereParams.searchError = `%${query.search}%`;
		}

		// Apply all where conditions at once
		if (whereConditions.length > 0) {
			queryBuilder.where(whereConditions.join(' AND '), whereParams);
		}

		// For better performance, we should consider adding composite indexes
		// for common query patterns in the entity definition, such as:
		// @Index(['userId', 'category', 'createdAt'])
		// @Index(['category', 'level', 'createdAt'])

		const page = Math.max(1, query.page ?? 1);
		const limit = query.limit ?? 20;

		const [logs, total] = await queryBuilder
			.orderBy('audit_log.createdAt', 'DESC')
			.skip((page - 1) * limit)
			.take(limit)
			.getManyAndCount();

		const result = { logs, total };

		if (this.enableRedisCache) {
			await this.redisService.set(cacheKey, JSON.stringify(result), this.CACHE_TTL);
		}

		return result;
	}

	/**
   * Get user activity summary with caching
   */
	async getUserActivitySummary(userId: number, days: number = 7): Promise<unknown> {
		try {
			this.logger.debug(`Getting user activity summary for userId: ${userId}, days: ${days}`);
			
			// Validate input parameters
			if (!userId) {
				throw new InvalidUserIdException('User ID is required');
			}
			
			if (days <= 0) {
				throw new InvalidDateException('Days must be greater than 0');
			}

			const cacheKey = this.buildCacheKey('user_summary', { userId, days });

			if (this.enableRedisCache) {
				const cached = await this.redisService.get<string>(cacheKey);
				if (cached) {
					this.logger.debug('Returning cached result');
					return JSON.parse(cached);
				}
			}

			const startDate = new Date();
			startDate.setDate(startDate.getDate() - days);
			this.logger.debug(`Start date for query: ${startDate}`);

			// Using a more efficient query with proper grouping and aggregation
			const summary = await this.auditLogRepository
				.createQueryBuilder('audit_log')
				.select('audit_log.category', 'category')
				.addSelect('COUNT(*)', 'count')
				.addSelect('COUNT(CASE WHEN audit_log.success = false THEN 1 END)', 'errorCount')
				.where('audit_log.userId = :userId', { userId })
				.andWhere('audit_log.createdAt >= :startDate', { startDate })
				.groupBy('audit_log.category')
				.orderBy('count', 'DESC') // Order by count for better presentation
				.getRawMany();
			
			this.logger.debug(`Query result: ${JSON.stringify(summary)}`);

			if (this.enableRedisCache) {
				await this.redisService.set(cacheKey, JSON.stringify(summary), this.CACHE_TTL);
			}

			return summary;
		} catch (error) {
			this.logger.error(`Error getting user activity summary for userId ${userId}: ${(error as Error).message}`, (error as Error).stack);
			throw error;
		}
	}

	/**
   * Process audit log (called by queue processor)
   */
	async processAuditLog(data: CreateAuditLogDto): Promise<void> {
		try {
			await this.processAndSaveLog(data);
			this.logger.debug(`Audit log processed: ${data.action} for user ${data.userId || 'system'}`);
		} catch (error) {
			this.logger.error('Failed to process audit log', (error as Error).stack);
			throw error;
		}
	}

	/**
   * Get recent logs from Redis cache
   */
	async getRecentLogs(userId?: number, limit: number = 20): Promise<unknown[]> {
		if (!this.enableRedisCache) {
			return [];
		}

		const redisKey = this.buildCacheKey('recent', userId || 'system');
		const logs = await this.redisService.lrange(redisKey, 0, limit - 1);
		return logs.map((log: string) => JSON.parse(log));
	}

	/**
   * Clear cache for user
   */
	async clearUserCache(userId: number): Promise<void> {
		if (!this.enableRedisCache) {
			return;
		}

		const pattern = `${this.CACHE_PREFIX}:*:${userId}:*`;
		const keys = await this.redisService.keys(pattern);
		if (keys.length > 0) {
			await Promise.all(keys.map((key: string) => this.redisService.del(key)));
		}
	}

	/**
   * Sanitize sensitive data
   */
	private sanitizeData(data: CreateAuditLogDto): CreateAuditLogDto {
		const sensitiveFields = ['password', 'token', 'secret', 'key', 'authorization'];

		const sanitizeObject = (
			obj: Record<string, unknown> | undefined,
		): Record<string, unknown> | undefined => {
			if (!obj || typeof obj !== 'object') return obj;

			const sanitized = { ...obj };
			for (const [key, value] of Object.entries(sanitized)) {
				if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
					sanitized[key] = '[REDACTED]';
				} else if (typeof value === 'object') {
					sanitized[key] = sanitizeObject(value as Record<string, unknown>);
				}
			}
			return sanitized;
		};

		return {
			...data,
			context: sanitizeObject(data.context),
			metadata: sanitizeObject(data.metadata),
		};
	}

	/**
	 * Build cache key
	 */
	private buildCacheKey(operation: string, data: unknown): string {
		// Sắp xếp các thuộc tính của đối tượng theo thứ tự bảng chữ cái để tạo chuỗi nhất quán
		const hash = hashCacheKey(this.sortObjectKeys(data));
		return this.redisKeyManager.custom(this.CACHE_PREFIX, operation, hash);
	}

	/**
	 * Sắp xếp các thuộc tính của đối tượng theo thứ tự bảng chữ cái
	 */
	private sortObjectKeys(obj: any): any {
		if (obj === null || typeof obj !== 'object') {
			return obj;
		}
		
		if (Array.isArray(obj)) {
			return obj.map(item => this.sortObjectKeys(item));
		}
		
		const sortedObj: Record<string, any> = {};
		Object.keys(obj).sort().forEach(key => {
			if (obj[key] !== undefined) {
				sortedObj[key] = this.sortObjectKeys(obj[key]);
			}
		});
		
		return sortedObj;
	}
}
