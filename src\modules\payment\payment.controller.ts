import { RoleType } from '@constants/role-type';
import { ApiCommonResponse } from '@decorators/api-common-response.decorator';
import { AuthUser } from '@decorators/auth-user.decorator';
import { Auth } from '@decorators/http.decorators';
import type { UserAccountEntity } from '@modules/user/user-account.entity';
import { Body, Controller, Get, HttpStatus, Param, ParseUUIDPipe, Post, Query, Req, ValidationPipe } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import type { Request } from 'express';

import type { GetPacksDto } from './dtos/get-pack.dto';
import { PackResponseDto } from './dtos/pack-response.dto';
import { CreateExchangePaymentDto, CreatePaymentFuncoinDto, CreatePaymentItemDto, CreatePaymentReponseDto, UpdatePaymentDto } from './dtos/payment.dto';
import type { QueryPaymentMethodDto } from './dtos/payment-method.dto';
import { PaymentTransactionEntity } from './entities/payment-transaction.entity';
import { PaymentService } from './payment.service';

@ApiTags('Payment')
@Controller('payment')
export class PaymentController {
	constructor(private readonly paymentService: PaymentService) {}

	@Get('funcoin-pack')
	@Auth([RoleType.USER])
	@ApiCommonResponse({
		status: HttpStatus.OK,
		description: 'Get list of funcoin packs',
		type: PackResponseDto,
	})
	async getFCoinPacks(): Promise<PackResponseDto[]> {
		const packs = await this.paymentService.getFunCoinPacks();
		return this.paymentService.sortPacks(packs);
	}

	@Get('item-pack')
	@Auth([RoleType.USER])
	@ApiCommonResponse({
		status: HttpStatus.OK,
		description: 'Get list of item packs',
		type: PackResponseDto,
	})
	@ApiQuery({ name: 'gameKey', required: true, type: String })
	@ApiQuery({ name: 'payWith', required: true, enum: ['CASH', 'FUNCOIN'] })
	async getItemPacks(@Query() query: GetPacksDto): Promise<PackResponseDto[]> {
		const packs = await this.paymentService.getItemPacks(query.gameKey, query.payWith);
		return this.paymentService.sortPacks(packs);
	}

	@Get('payment-method')
	async getAll(@Query() query?: QueryPaymentMethodDto) {
		return this.paymentService.findAllPaymentMethod(query?.status);
	}

	@Post('buy-funcoin')
	@Auth([RoleType.USER])
	@ApiOperation({ summary: 'Create a new payment using Cash buy Funcoin' })
	@ApiBody({ type: CreatePaymentFuncoinDto })
	@ApiCommonResponse({
		status: HttpStatus.CREATED,
		description: 'Payment created successfully',
		type: CreatePaymentReponseDto,
	})
	async createPaymentFuncoin(
		@Body(ValidationPipe) createPaymentDto: CreatePaymentFuncoinDto,
		@AuthUser() user: UserAccountEntity,
		@Req() request: Request,
	): Promise<CreatePaymentReponseDto> {
		return this.paymentService.createCashPayment(createPaymentDto, user, request);
	}

	@Post('buy-item')
	@Auth([RoleType.USER])
	@ApiOperation({ summary: 'Create a new payment using Cash buy Item' })
	@ApiBody({ type: CreatePaymentItemDto })
	@ApiCommonResponse({
		status: HttpStatus.CREATED,
		description: 'Payment created successfully',
		type: CreatePaymentReponseDto,
	})
	async createPaymentItem(
		@Body(ValidationPipe) createPaymentDto: CreatePaymentItemDto,
		@AuthUser() user: UserAccountEntity,
		@Req() request: Request,
	): Promise<CreatePaymentReponseDto> {
		return this.paymentService.createCashPayment(createPaymentDto, user, request);
	}

	@Post('funcoin-exchange')
	@Auth([RoleType.USER])
	@ApiOperation({ summary: 'Create a new exchange payment' })
	@ApiBody({ type: CreateExchangePaymentDto })
	@ApiCommonResponse({
		status: HttpStatus.CREATED,
		description: 'Exchange Payment created successfully',
		type: PaymentTransactionEntity,
	})
	async createExchangePayment(
		@Body(ValidationPipe) createExchangePaymentDto: CreateExchangePaymentDto,
		@AuthUser() user: UserAccountEntity,
		@Req() request: Request,
	): Promise<PaymentTransactionEntity> {
		return this.paymentService.createExchangePayment(createExchangePaymentDto, user, request);
	}

	@Get()
	// @Auth([RoleType.USER])
	@ApiOperation({ summary: 'Get all payments' })
	@ApiCommonResponse({
		status: HttpStatus.OK,
		description: 'Returns all payments',
		type: [PaymentTransactionEntity],
	})
	async findAllPayments(): Promise<PaymentTransactionEntity[]> {
		return this.paymentService.findAllPayment();
	}

	@Get(':txId')
	@Auth([RoleType.USER])
	@ApiOperation({ summary: 'Get payment by txId' })
	@ApiParam({
		name: 'txId',
		description: 'Payment transaction UUID',
		example: '123e4567-e89b-12d3-a456-************',
	})
	@ApiCommonResponse({
		status: HttpStatus.OK,
		description: 'Payment found',
		type: PaymentTransactionEntity,
	})
	async findByIdPayment(@Param('txId', ParseUUIDPipe) txId: string): Promise<PaymentTransactionEntity> {
		return this.paymentService.findById(txId);
	}

	// @Get('pack/:packKey')
	// @Auth([RoleType.USER])
	@ApiOperation({ summary: 'Get payment by pack key' })
	@ApiParam({
		name: 'packKey',
		description: 'Pack key identifier',
		example: 'PACK-001',
	})
	@ApiCommonResponse({
		description: 'Payment found',
		type: PaymentTransactionEntity,
	})
	async findByPackKeyPayment(@Param('packKey') packKey: string): Promise<PaymentTransactionEntity> {
		return this.paymentService.findByPackKey(packKey);
	}

	// @Put(':txId/status')
	// @Auth([RoleType.USER])
	@ApiOperation({ summary: 'Update payment status' })
	@ApiParam({
		name: 'txId',
		description: 'Payment transaction UUID',
		example: '123e4567-e89b-12d3-a456-************',
	})
	@ApiBody({ type: UpdatePaymentDto })
	@ApiCommonResponse({
		status: HttpStatus.OK,
		description: 'Payment status updated successfully',
		type: PaymentTransactionEntity,
	})
	async updatePaymentStatus(
		@Param('txId', ParseUUIDPipe) txId: string,
		@Body(ValidationPipe) updatePaymentDto: UpdatePaymentDto,
	): Promise<PaymentTransactionEntity> {
		return this.paymentService.updatePaymentStatus(txId, updatePaymentDto);
	}
}
