// src/modules/vip/dto/vip-tier.dto.ts
import { PartialType } from '@nestjs/mapped-types';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { <PERSON>Array, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';

import { VipBenefitDto } from './vip-benefit.dto';

export class VipTierDto {
	@ApiProperty({
		description: 'VIP tier id',
		example: 3,
	})
	@IsString()
	id!: number;

	@ApiProperty({
		description: 'Name of the VIP tier',
		example: 'Gold Tier',
	})
	@IsString()
	tierName!: string;

	@ApiPropertyOptional({
		description: 'Description of the VIP tier',
		example: 'Exclusive benefits for gold members',
	})
	@IsOptional()
	@IsString()
	description?: string;

	@ApiProperty({
		description: 'Minimum recharge value required for this tier',
		example: 1000,
	})
	@IsNumber()
	@Type(() => Number)
	value!: number;

	@ApiPropertyOptional({
		description: 'Array of benefit IDs associated with this tier',
		type: [VipBenefitDto],
	})
	@IsOptional()
	@IsArray()
	@Type(() => VipBenefitDto)
	benefits?: VipBenefitDto[];
}

export class UpdateVipTierDto extends PartialType(VipBenefitDto) {}
