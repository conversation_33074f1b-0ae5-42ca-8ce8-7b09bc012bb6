import type { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPlatformToUserAndAuditLog1756649984447 implements MigrationInterface {
	name = 'AddPlatformToUserAndAuditLog1756649984447';

	public async up(queryRunner: QueryRunner): Promise<void> {
		// Add platform column to user_account table (nullable)
		await queryRunner.query(`
			ALTER TABLE "user_account"
			ADD COLUMN "platform" character varying(20)
		`);

		// Add platform column to audit_log table (nullable first)
		await queryRunner.query(`
			ALTER TABLE "audit_log"
			ADD COLUMN "platform" character varying(20)
		`);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		// Remove platform column from audit_log table
		await queryRunner.query('ALTER TABLE "audit_log" DROP COLUMN "platform"');

		// Remove platform column from user_account table
		await queryRunner.query('ALTER TABLE "user_account" DROP COLUMN "platform"');
	}
}
