import { PaymentMethodStatus, PaymentProvider, PaymentStatus, Platform } from '@constants/index';
import { GameEntity } from '@modules/game/game.entity';
import type { UserAccountEntity } from '@modules/user/user-account.entity';
import { UserGameMappingEntity } from '@modules/user/user-game-mapping.entity';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { PlatformService } from '@shared/services/platform.service.ts';
import { NotFoundException } from 'exceptions';
import type { Request } from 'express';
import { Repository } from 'typeorm';

import { PackResponseDto } from './dtos/pack-response.dto';
import { type CreateExchangePaymentDto, CreatePaymentFuncoinDto, type CreatePaymentItemDto, type UpdatePaymentDto } from './dtos/payment.dto';
import type { UpdatePaymentMethodStatusDto } from './dtos/payment-method.dto';
import { FunCoinPack } from './entities/funcoin-pack.entity';
import { ItemPack } from './entities/item-pack.entity';
import { PaymentMethodEntity } from './entities/payment-method.entity';
import { PaymentTransactionEntity } from './entities/payment-transaction.entity';

// Constants for better maintainability
const TAG_PRIORITY = {
	'HOT': 1,
	'SALE': 2,
	'NEW': 3,
	'undefined': 4,
} as const;

type PackType = FunCoinPack | ItemPack;

type CreatePaymentParams = {
	pack: PackType;
	userId: number;
	provider: PaymentProvider;
	platform: string;
	finalPrice: number;
	gameKey?: string;
	paymentMethod?: string;
	note?: string;
};

@Injectable()
export class PaymentService {
	constructor(
		@InjectRepository(FunCoinPack)
		private readonly funCoinPackRepository: Repository<FunCoinPack>,
		@InjectRepository(ItemPack)
		private readonly itemPackRepository: Repository<ItemPack>,
		@InjectRepository(PaymentMethodEntity)
		private readonly paymentMethodRepository: Repository<PaymentMethodEntity>,
		@InjectRepository(GameEntity)
		private readonly gameRepository: Repository<GameEntity>,
		@InjectRepository(PaymentTransactionEntity)
		private readonly paymentRepository: Repository<PaymentTransactionEntity>,
		@InjectRepository(UserGameMappingEntity)
		private readonly userGameMappingRepository: Repository<UserGameMappingEntity>,
		private readonly platformService: PlatformService,
	) {}

	private calculateFinalPrice(pack: PackType, now: Date = new Date()) {
		const price = Number(pack.price);
		const discount = Number(pack.discount);
		let finalPrice = price;

		const isPromotionActive =
			pack.promotionStartDate &&
			pack.promotionEndDate &&
			now >= pack.promotionStartDate &&
			now <= pack.promotionEndDate;

		const validTag = isPromotionActive ? pack.tag : undefined;
		const activeDiscount = isPromotionActive ? discount : 0;

		if (isPromotionActive) {
			finalPrice = Math.floor((price * (100 - discount)) / 100);
		}

		return {
			validTag,
			discount: activeDiscount,
			price,
			finalPrice,
		};
	}

	async getFunCoinPacks(): Promise<PackResponseDto[]> {
		const packs = await this.funCoinPackRepository.find({
			where: { isActive: true },
		});

		if (!packs?.length) {
			throw new NotFoundException('Packs not found');
		}

		const now = new Date();
		return packs.map(pack => {
			// eslint-disable-next-line @typescript-eslint/no-unused-vars
			const { isActive, quantity, point, ...rest } = pack;
			const { validTag, discount, price, finalPrice } = this.calculateFinalPrice(pack, now);

			return {
				...rest,
				tag: validTag,
				point: Number(point),
				price,
				discount,
				finalPrice,
				packImageUrl: pack.packImageUrl,
			};
		});
	}

	async getItemPacks(gameKey?: string, payWith?: 'CASH' | 'FUNCOIN'): Promise<PackResponseDto[]> {
		const where: Partial<ItemPack> = { isActive: true };

		if (gameKey) where.gameKey = gameKey;
		if (payWith) where.payWith = payWith;

		const packs = await this.itemPackRepository.find({ where });

		if (!packs?.length) {
			throw new NotFoundException('Packs not found');
		}

		const now = new Date();
		return packs.map(pack => {
			// eslint-disable-next-line @typescript-eslint/no-unused-vars
			const { isActive, quantity, point, ...rest } = pack;
			const { validTag, discount, price, finalPrice } = this.calculateFinalPrice(pack, now);

			return {
				...rest,
				payWith: pack.payWith as 'CASH' | 'FUNCOIN',
				tag: validTag,
				point: Number(point),
				price,
				discount,
				finalPrice,
				packImageUrl: pack.packImageUrl,
			};
		});
	}

	sortPacks(result: PackResponseDto[]): PackResponseDto[] {
		return result.sort((a, b) => {
			const tagA = TAG_PRIORITY[a.tag ?? 'undefined'];
			const tagB = TAG_PRIORITY[b.tag ?? 'undefined'];

			if (tagA !== tagB) {
				return tagA - tagB;
			}

			return a.finalPrice - b.finalPrice;
		});
	}

	async findAllPaymentMethod(status?: PaymentMethodStatus) {
		const whereCondition = status !== undefined ? { where: { status } } : {};
		return this.paymentMethodRepository.find(whereCondition);
	}

	async updatePaymentMethodStatus(dto: UpdatePaymentMethodStatusDto) {
		const { paymentMethod, status } = dto;
		await this.paymentMethodRepository.update({ paymentMethod }, { status });
		return this.paymentMethodRepository.findOneBy({ paymentMethod });
	}

	async createPayment({
		pack,
		userId,
		provider,
		platform,
		finalPrice,
		gameKey,
		paymentMethod,
		note,
	}: CreatePaymentParams): Promise<PaymentTransactionEntity> {
		const payment = this.paymentRepository.create({
			userId,
			gameKey,
			packKey: pack.packKey,
			amount: finalPrice,
			currency: pack.currency,
			provider,
			paymentMethod,
			status: PaymentStatus.PENDING,
			platform,
			note,
			context: {},
		});

		return this.paymentRepository.save(payment);
	}

	async createCashPayment(
		createPaymentDto: CreatePaymentFuncoinDto | CreatePaymentItemDto,
		user: UserAccountEntity,
		req: Request,
	): Promise<PaymentTransactionEntity> {

		const platform = this.platformService.detectPlatform(req, undefined);
		var

		if (createPaymentDto instanceof CreatePaymentFuncoinDto) {
			// Buy Funcoins
			const { packKey, paymentMethod } = createPaymentDto;
			const pack = await this.funCoinPackRepository.findOne({
				where: {
					packKey,
					isActive: true,
				},
			});
			if (!pack) throw new NotFoundException('Pack not found!');

			const { finalPrice } = this.calculateFinalPrice(pack, new Date());

			return this.createPayment({
				pack,
				userId: user.userId,
				provider: PaymentProvider.ONEPAY,
				platform,
				finalPrice,
				gameKey: undefined,
				paymentMethod,
			});
		} else {
			// Buy items by cash
			var { gameKey, packKey, paymentMethod } = createPaymentDto;
			// Buy items by cash
			const [game, pack] = await Promise.all([
				this.gameRepository.findOne({ where: { gameKey } }),
				this.itemPackRepository.findOne({ where: { packKey } }),
			]);

			if (!game) throw new NotFoundException('Game not found');
			if (!pack) throw new NotFoundException('Pack not found!');

			const userGameMapping = await this.userGameMappingRepository.findOne({
				where: { userId: user.userId, gameId: game.gameId },
			});

			if (!userGameMapping) {
				throw new NotFoundException('User game mapping not found!');
			}

			const { finalPrice } = this.calculateFinalPrice(pack, new Date());
			const note = 'Payment for pack ' + pack.packName;

		}

		return this.createPayment({
			pack,
			userId: user.userId,
			provider: PaymentProvider.ONEPAY,
			platform,
			finalPrice,
			gameKey,
			paymentMethod,
			note,
		});
	}

	async createExchangePayment(
		createPaymentDto: CreateExchangePaymentDto,
		user: UserAccountEntity,
		req: Request,
	): Promise<PaymentTransactionEntity> {
		const { gameKey, packKey } = createPaymentDto;

		const platform = req ?
			this.platformService.detectPlatform(req, undefined) : Platform.WINDOWS;

		const [game, pack] = await Promise.all([
			this.gameRepository.findOne({ where: { gameKey } }),
			this.itemPackRepository.findOne({ where: { packKey } }),
		]);

		if (!game) throw new NotFoundException('Game not found');
		if (!pack) throw new NotFoundException('Pack not found!');

		const userGameMapping = await this.userGameMappingRepository.findOne({
			where: { userId: user.userId, gameId: game.gameId },
		});

		if (!userGameMapping) {
			throw new NotFoundException('User game mapping not found!');
		}

		const { finalPrice } = this.calculateFinalPrice(pack, new Date());
		const note = 'Exchange funcoins for pack ' + pack.packName;

		const payment = await this.createPayment({
			pack,
			userId: user.userId,
			provider: PaymentProvider.FUNFINITY,
			platform,
			finalPrice,
			gameKey,
			note,
		});

		return this.processExchangePayment(payment.txId, user);
	}

	async processExchangePayment(
		paymentId: string,
		user: UserAccountEntity,
	): Promise<PaymentTransactionEntity> {
		const payment = await this.findById(paymentId);

		if (payment.amount > user.userBalance) {
			payment.status = PaymentStatus.FAILED;
			payment.context = { reason: 'Not enough balance' };
			return this.paymentRepository.save(payment);
		}

		user.userBalance -= payment.amount;
		return this.processSucceedPayment(payment, user.userId);
	}

	async processSucceedPayment(
		payment: PaymentTransactionEntity,
		userId: number,
	): Promise<PaymentTransactionEntity> {
		const game = await this.gameRepository.findOne({ where: { gameKey: payment.gameKey } });

		if (game) {
			const userGameMapping = await this.userGameMappingRepository.findOne({
				where: { gameId: game.gameId, userId },
			});

			const gameBalance = userGameMapping?.gameBalance || 0;
			await this.userGameMappingRepository.update(
				{ userId },
				{ gameBalance: gameBalance + payment.amount },
			);
		}

		payment.status = PaymentStatus.SUCCESS;
		return this.paymentRepository.save(payment);
	}

	async findById(txId: string): Promise<PaymentTransactionEntity> {
		const payment = await this.paymentRepository.findOne({ where: { txId } });
		if (!payment) {
			throw new NotFoundException(`Payment with txId ${txId} not found`);
		}
		return payment;
	}

	async findByPackKey(packKey: string): Promise<PaymentTransactionEntity> {
		const payment = await this.paymentRepository.findOne({ where: { packKey } });
		if (!payment) {
			throw new NotFoundException(`Payment with packKey ${packKey} not found`);
		}
		return payment;
	}

	async findByGameKey(gameKey: string): Promise<PaymentTransactionEntity> {
		const payment = await this.paymentRepository.findOne({ where: { gameKey } });
		if (!payment) {
			throw new NotFoundException(`Payment with gameKey ${gameKey} not found`);
		}
		return payment;
	}

	async updatePaymentStatus(
		txId: string,
		updatePaymentDto: UpdatePaymentDto,
	): Promise<PaymentTransactionEntity> {
		const payment = await this.findById(txId);
		payment.status = updatePaymentDto.status;
		return this.paymentRepository.save(payment);
	}

	async findPendingPayments(): Promise<PaymentTransactionEntity[]> {
		return this.paymentRepository.find({
			where: { status: PaymentStatus.PENDING },
		});
	}

	async findAllPayment(): Promise<PaymentTransactionEntity[]> {
		return this.paymentRepository.find();
	}
}
