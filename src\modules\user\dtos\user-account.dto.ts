import { AbstractDto } from '@common/dto/abstract.dto';
import { UserAccountStatus, UserAccountType } from '@constants/user';
import {
	BooleanField,
	DateFieldOptional,
	EmailFieldOptional,
	EnumField,
	NumberField,
	StringFieldOptional,
	VietnamesePhoneFieldOptional,
} from '@decorators/field.decorators';
import { Exclude } from 'class-transformer';

import type { UserAccountEntity } from '../user-account.entity';

export class UserAccountDto extends AbstractDto {
	@NumberField()
	// @Expose({ name: 'user_id' })
	userId!: number;

	@StringFieldOptional({ nullable: true })
	// @Expose({ name: 'username' })
	username!: string | null;

	@EmailFieldOptional({ nullable: true })
	// @Expose({ name: 'email' })
	email?: string | null;

	@VietnamesePhoneFieldOptional({ nullable: true })
	// @Expose({ name: 'phone' })
	phone?: string | null;

	@BooleanField()
	isEmailVerified!: boolean;

	@BooleanField()
	isPhoneVerified!: boolean;

	@StringFieldOptional()
	@Exclude()
	passwordHash?: string | null;

	@StringFieldOptional()
	@Exclude()
	refreshToken?: string | null;

	@DateFieldOptional()
	@Exclude()
	refreshTokenExpiresAt?: Date | null;

	@EnumField(() => UserAccountStatus)
	// @Expose({ name: 'status' })
	status: UserAccountStatus;

	@EnumField(() => UserAccountType)
	// @Expose({ name: 'account_type' })
	accountType: UserAccountType;

	@StringFieldOptional()
	// @Expose({ name: 'social_uid' })
	socialUid?: string | null;

	@DateFieldOptional()
	// @Expose({ name: 'linked_at' })
	linkedAt?: Date | null;

	@StringFieldOptional()
	// @Expose({ name: 'social_access_token' })
	socialAccessToken?: string | null;

	@StringFieldOptional()
	// @Expose({ name: 'social_refresh_token' })
	socialRefreshToken?: string | null;

	@NumberField()
	// @Expose({ name: 'user_balance' })
	userBalance!: number;

	@DateFieldOptional()
	// @Expose({ name: 'last_login_at' })
	declare lastLoginAt?: Date | null;

	@StringFieldOptional()
	// @Expose({ name: 'created_at_ip' })
	createdAtIp?: string | null;

	@StringFieldOptional()
	// @Expose({ name: 'last_login_at_ip' })
	lastLoginAtIp?: string | null;

	@NumberField()
	// @Expose({ name: 'total_recharge' })
	totalRecharge!: number;

	@NumberField()
	// @Expose({ name: 'total_consume' })
	totalConsume!: number;

	constructor(userAccount: UserAccountEntity) {
		super(userAccount);
		this.userId = userAccount.userId;
		this.username = userAccount.username ?? null;
		this.email = userAccount.email;
		this.phone = userAccount.phone;
		this.isEmailVerified = userAccount.isEmailVerified;
		this.isPhoneVerified = userAccount.isPhoneVerified;
		this.passwordHash = userAccount.passwordHash;
		this.refreshToken = userAccount.refreshToken;
		this.refreshTokenExpiresAt = userAccount.refreshTokenExpiresAt;
		this.status = userAccount.status as UserAccountStatus;
		this.accountType = userAccount.accountType as UserAccountType;
		this.socialUid = userAccount.socialUid;
		this.linkedAt = userAccount.linkedAt;
		this.socialAccessToken = userAccount.socialAccessToken;
		this.socialRefreshToken = userAccount.socialRefreshToken;
		this.userBalance = userAccount.userBalance;
		this.lastLoginAt = userAccount.lastLoginAt;
		this.createdAtIp = userAccount.createdAtIp;
		this.lastLoginAtIp = userAccount.lastLoginAtIp;
		this.totalRecharge = userAccount.totalRecharge;
		this.totalConsume = userAccount.totalConsume;
	}
}