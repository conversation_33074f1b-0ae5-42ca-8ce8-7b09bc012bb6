import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('item_pack')
export class ItemPack {
	@PrimaryGeneratedColumn('increment')
	id!: number;

	@Column({ type: 'varchar', unique: true })
	packKey!: string;

	@Column({ type: 'varchar' })
	packName!: string;

	@Column({ type: 'int' })
	quantity!: number;

	@Column({ type: 'decimal', precision: 10, scale: 2 })
	point!: number;

	@Column({ type: 'decimal', precision: 10, scale: 2 })
	price!: number;

	@Column({ type: 'varchar' })
	currency!: string;

	@Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
	discount!: number;

	@Column({ type: 'varchar', nullable: true })
	tag!: 'HOT' | 'NEW' | 'SALE';

	@Column({ type: 'boolean', default: true })
	isActive!: boolean;

	@Column({ type: 'varchar' })
	payWith!: 'CASH' | 'FUNCOIN';

	@Column({ type: 'varchar' })
	gameKey!: string;

	@Column({ type: 'varchar', nullable: true })
	packImageUrl?: string;

	@Column({ type: 'timestamp', nullable: true })
	promotionStartDate?: Date;

	@Column({ type: 'timestamp', nullable: true })
	promotionEndDate?: Date;
}
