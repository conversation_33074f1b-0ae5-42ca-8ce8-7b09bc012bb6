x-common-env: &common-env
  USE_INFISICAL: true
  ENVIRONMENT: prod
  PROJECT_ID: 94f0c787-8eb0-4216-84f4-a4b6c15d43c5
  CLIENT_ID:
  CLIENT_SECRET:
  INTERNAL_REDIS_HOST: host.docker.internal
  INTERNAL_REDIS_PORT: 6379

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    networks:
      - caddy
    labels:
      caddy: playerservice.fungames.vn
      caddy.reverse_proxy: "{{upstreams 4001}}"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      <<: *common-env
  payment:
    build:
      context: .
      dockerfile: Dockerfile.payment
    networks:
      - caddy
    labels:
      caddy: paymentservice.fungames.vn
      caddy.reverse_proxy: "{{upstreams 4002}}"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      <<: *common-env

networks:
  caddy:
    external: true
