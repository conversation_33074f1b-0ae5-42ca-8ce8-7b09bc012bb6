import { type MigrationInterface, type QueryRunner, Table } from 'typeorm';

export class CreateCoinPacks1756649984445 implements MigrationInterface {
	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.createTable(
			new Table({
				name: 'funcoin_pack',
				columns: [
					{ name: 'id', type: 'int', isPrimary: true, isGenerated: true, generationStrategy: 'increment' },
					{ name: 'pack_key', type: 'varchar', isUnique: true },
					{ name: 'pack_name', type: 'varchar' },
					{ name: 'quantity', type: 'int' },
					{ name: 'point', type: 'decimal', precision: 10, scale: 2 },
					{ name: 'price', type: 'decimal', precision: 10, scale: 2 },
					{ name: 'currency', type: 'varchar' },
					{ name: 'discount', type: 'decimal', precision: 5, scale: 2, default: 0 },
					{ name: 'tag', type: 'varchar', isNullable: true },
					{ name: 'is_active', type: 'boolean', default: true },
					{ name: 'pack_image_url', type: 'varchar', isNullable: true },
					{ name: 'promotion_start_date', type: 'timestamp', isNullable: true },
					{ name: 'promotion_end_date', type: 'timestamp', isNullable: true },
				],
			}),
			true,
		);

		await queryRunner.createTable(
			new Table({
				name: 'item_pack',
				columns: [
					{ name: 'id', type: 'int', isPrimary: true, isGenerated: true, generationStrategy: 'increment' },
					{ name: 'pack_key', type: 'varchar', isUnique: true },
					{ name: 'pack_name', type: 'varchar' },
					{ name: 'quantity', type: 'int' },
					{ name: 'point', type: 'decimal', precision: 10, scale: 2 },
					{ name: 'price', type: 'decimal', precision: 10, scale: 2 },
					{ name: 'currency', type: 'varchar' },
					{ name: 'discount', type: 'decimal', precision: 5, scale: 2, default: 0 },
					{ name: 'tag', type: 'varchar', isNullable: true },
					{ name: 'is_active', type: 'boolean', default: true },
					{ name: 'pay_with', type: 'varchar' },
					{ name: 'game_key', type: 'varchar' },
					{ name: 'pack_image_url', type: 'varchar', isNullable: true },
					{ name: 'promotion_start_date', type: 'timestamp', isNullable: true },
					{ name: 'promotion_end_date', type: 'timestamp', isNullable: true },
				],
			}),
			true,
		);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.dropTable('item_pack');
		await queryRunner.dropTable('funcoin_pack');
	}
}
