#!/usr/bin/env tsx

import { JwtService } from '@nestjs/jwt';
import * as dotenv from 'dotenv';
import * as path from 'path';

import { RoleType } from '../constants/role-type';
import { TokenType } from '../constants/token-type';

// Load environment variables from .env file
dotenv.config({ path: path.resolve(process.cwd(), '.env') });

async function createAdminJwt() {
	// Get userId from command line arguments
	const userId = process.argv[2] ? parseInt(process.argv[2], 10) : 1;
	
	if (isNaN(userId) || userId <= 0) {
		console.error('❌ Invalid userId provided. Please provide a positive integer.');
		process.exit(1);
	}

	console.info(`🚀 Starting user JWT creation for userId: ${userId}\n`);

	const rawPrivateKey = process.env.JWT_PRIVATE_KEY;
	const rawPublicKey = process.env.JWT_PUBLIC_KEY;

	if (!rawPrivateKey || !rawPublicKey) {
		console.error('❌ Missing JWT_PRIVATE_KEY or JWT_PUBLIC_KEY in environment');
		process.exit(1);
	}

	// Handle escaped newlines (e.g., from .env)
	const privateKey = rawPrivateKey.replace(/\\n/g, '\n');
	const publicKey = rawPublicKey.replace(/\\n/g, '\n');

	const jwtService = new JwtService({
		privateKey,
		publicKey,
		signOptions: {
			algorithm: 'RS256',
		},
		verifyOptions: {
			algorithms: ['RS256'],
		},
	});

	try {
		const token = await jwtService.signAsync(
			{
				userId,
				type: TokenType.ACCESS_TOKEN,
				role: RoleType.USER,
			},
			{
				expiresIn: '1h',
			},
		);

		console.info('✅ User ACCESS_TOKEN created successfully!');
		console.info('🔐 Algorithm: RS256');
		console.info('⏲️  Expires In: 1h\n');
		console.info('🪪 JWT:');
		console.info(token);

		// Optional quick verification to ensure token is valid with provided public key
		const payload = await jwtService.verifyAsync(token).catch(() => null);
		if (payload) {
			const exp = new Date(payload.exp * 1000).toLocaleString('en-CA', {
				year: 'numeric',
				month: '2-digit',
				day: '2-digit',
				hour: '2-digit',
				minute: '2-digit',
				second: '2-digit',
				hour12: true,
			});

			const iat = new Date(payload.iat * 1000).toLocaleString('en-CA', {
				year: 'numeric',
				month: '2-digit',
				day: '2-digit',
				hour: '2-digit',
				minute: '2-digit',
				second: '2-digit',
				hour12: true,
			});
			console.info('\n📦 Payload preview:', {
				userId: payload.userId,
				type: payload.type,
				role: payload.role,
				createdAt: iat,
				expiredAt: exp,
			});
		}
	} catch (error) {
		console.error(
			'❌ Failed to create user JWT:',
			error instanceof Error ? error.message : 'Unknown error',
		);
		process.exit(1);
	}
}

// Run the script
createAdminJwt();
