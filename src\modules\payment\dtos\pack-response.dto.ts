import { ApiProperty } from '@nestjs/swagger';

export class PackResponseDto {
	@ApiProperty()
	id!: number;

	@ApiProperty()
	packKey!: string;

	@ApiProperty()
	packName!: string;
	
	@ApiProperty()
	point!: number;

	@ApiProperty()
	price!: number;

	@ApiProperty()
	currency!: string;

	@ApiProperty()
	discount!: number;

	@ApiProperty({ required: false })
	tag?: 'HOT' | 'NEW' | 'SALE' | null;

	@ApiProperty()
	payWith?: 'CASH' | 'FUNCOIN';

	@ApiProperty()
	gameKey?: string;

	@ApiProperty()
	finalPrice!: number;

	@ApiProperty()
	packImageUrl?: string;
}
