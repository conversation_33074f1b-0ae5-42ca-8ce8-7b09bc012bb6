import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { CqrsModule } from '@nestjs/cqrs';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApiConfigService } from '@shared/services/api-config.service';

import { SharedModule } from '../../shared/shared.module';
import { AuditLogController } from './audit-log.controller';
import { AuditLogService } from './audit-log.service';
import { AuditLogEntity } from './entities/audit-log.entity';
import { AuditLogInterceptor } from './interceptors/audit-log.interceptor';
import {
	PasswordChangedHandler,
	PasswordResetHandler,
	RefreshTokenFailedHandler,
	RefreshTokenSuccessHandler,
	UserLoggedInHandler,
	User<PERSON><PERSON><PERSON>utHandler,
	UserPro<PERSON>le<PERSON>p<PERSON><PERSON><PERSON><PERSON>,
	<PERSON>r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	VerifyContact<PERSON>andler,
} from './listeners/audit-log.listener';
import { AuditLogProcessor } from './processors/audit-log.processor';

const eventHandlers = [
	UserLoggedInHandler,
	UserRegisteredHandler,
	UserLoggedOutHandler,
	PasswordChangedHandler,
	PasswordResetHandler,
	RefreshTokenSuccessHandler,
	RefreshTokenFailedHandler,
	UserProfileUpdatedHandler,
	VerifyContactHandler,
];

@Module({
	imports: [
		TypeOrmModule.forFeature([AuditLogEntity]),
		BullModule.registerQueueAsync({
			name: 'audit-log',
			imports: [SharedModule],
			useFactory: (configService: ApiConfigService) => ({
				redis: {
					host: configService.redisConfig.socket.host,
					port: configService.redisConfig.socket.port,
				},
			}),
			inject: [ApiConfigService],
		}),
		SharedModule,
		ConfigModule,
		CqrsModule,
	],
	providers: [
		AuditLogService,
		AuditLogProcessor,
		AuditLogInterceptor,
		...eventHandlers,
	],
	controllers: [AuditLogController],
	exports: [AuditLogService, TypeOrmModule.forFeature([AuditLogEntity]), BullModule],
})
export class AuditLogModule {}
