import { InfisicalSDK } from '@infisical/sdk';
import { config } from 'dotenv';
import { MissingConfigCredentialsException } from 'exceptions';

config();

/**
 * Loads environment variables from Infisical if configured to do so.
 * It checks for `USE_INFISICAL=true` in the environment.
 * If true, it fetches all secrets from the configured project and environment
 * and populates `process.env` with them.
 */
export async function loadEnvFromInfisical() {
	if (process.env.USE_INFISICAL !== 'true') {
		return;
	}

	const clientId = process.env.CLIENT_ID;
	const clientSecret = process.env.CLIENT_SECRET;
	const projectId = process.env.PROJECT_ID;
	const environment = process.env.ENVIRONMENT;

	if (!clientId || !clientSecret || !projectId || !environment) {
		throw new MissingConfigCredentialsException();
	}

	const infisical = new InfisicalSDK({ siteUrl: 'https://eu.infisical.com' });
	await infisical.auth().universalAuth.login({ clientId, clientSecret });

	const secretNames = [
		'NODE_ENV',
		'PORT',
		'ENABLE_ORM_LOGS',
		'ENABLE_DOCUMENTATION',
		'API_VERSION',
		'JWT_PRIVATE_KEY',
		'JWT_PUBLIC_KEY',
		'JWT_EXPIRATION_TIME',
		'JWT_REFRESH_TOKEN_EXPIRATION_TIME',
		'DB_HOST',
		'DB_PORT',
		'DB_USERNAME',
		'DB_PASSWORD',
		'DB_DATABASE',
		'REDIS_HOST',
		'REDIS_PORT',
		'REDIS_TTL',
		'THROTTLER_TTL',
		'THROTTLER_LIMIT',
		'GOOGLE_CLIENT_ID',
		'GOOGLE_CLIENT_SECRET',
		'FACEBOOK_CLIENT_ID',
		'FACEBOOK_CLIENT_SECRET',
		'ZALO_CLIENT_ID',
		'ZALO_CLIENT_SECRET',
		'CORS_ORIGINS',
		'FRONTEND_URL',
		'BACKEND_URL',
		'AUDIT_LOG_DATABASE_ENABLED',
		'AUDIT_LOG_REDIS_CACHE_ENABLED',
		'MAILER_API_HOST',
		'PORT_PAYMENT',
	];

	await Promise.all(
		secretNames.map(async(name) => {
			const secret = await infisical.secrets().getSecret({
				environment,
				projectId,
				secretPath: '/',
				secretName: name,
			});

			if (secret.secretValue) {
				process.env[name] = secret.secretValue;
			}
		}),
	);
}
