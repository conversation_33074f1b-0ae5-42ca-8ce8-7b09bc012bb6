import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class InitQuickplay1756351746068 implements MigrationInterface {
	name = 'InitQuickplay1756351746068';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`CREATE TABLE "quickplay" (
			"quickplay_id" character varying(50) NOT NULL,
			"game_key" character varying(50) NOT NULL,
			"platform" character varying(20) NOT NULL,
			"device_id" character varying(100) NOT NULL,
			"ip_address" character varying(50) NOT NULL,
			"created_at" TIMESTAMP NOT NULL DEFAULT now(), 
			CONSTRAINT "PK_67274b82fd9f0be11c90ae2bcf2" PRIMARY KEY ("quickplay_id"))`);
		await queryRunner.query(`CREATE UNIQUE INDEX 
			"quickplay_quickplay_id_key" ON "quickplay" ("quickplay_id") `);

		await queryRunner.query(`INSERT INTO sys_config (key, value, description, data_type, category) VALUES 
			('quickplay_limit_ttl', '5', 'Quickplay limit TTL in minutes', 'number', 'quickplay'),
			('quickplay_max_attempts', '10', 'Quickplay max attempts', 'number', 'quickplay')`);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`DROP INDEX 
			"public"."quickplay_quickplay_id_key"`);
		await queryRunner.query(`DROP TABLE 
			"quickplay"`);
	}
}
