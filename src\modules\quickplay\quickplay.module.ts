import { type DynamicModule, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { QuickplayController } from './quickplay.controller';
import { QuickplayEntity } from './quickplay.entity';
import { QuickplayService } from './quickplay.service';

@Module({
	imports: [
		TypeOrmModule.forFeature([
			QuickplayEntity,
		]),
	],
	exports: [QuickplayService],
	providers: [QuickplayService],
})
export class QuickplayModule {
	static forRoot(options: { isPaymentService: boolean }): DynamicModule {
		return {
			module: QuickplayModule,
			controllers: options.isPaymentService ? [] : [QuickplayController],
		};
	}
}
