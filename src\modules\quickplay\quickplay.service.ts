import { hashCacheKey } from '@common/utils.ts';
import { SocialCode, UserAccountType } from '@constants/user';
import { SysConfigService } from '@modules/sys-config/sys-config.service.ts';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { RedisService } from '@shared/services/redis.service.ts';
import { RedisKeyManagerService } from '@shared/services/redis-key-manager.service.ts';
import { TooManyRequestsException } from 'exceptions/general.exception.ts';
import { GeneratorProvider } from 'providers/generator.provider.ts';
import { Repository } from 'typeorm';

import { QuickplayDto } from './dtos/quickplay.dto.ts';
import { QuickplayResponseDto } from './dtos/quickplay-response.dto.ts';
import { QuickplayEntity } from './quickplay.entity.ts';

@Injectable()
export class QuickplayService {
	constructor(
		@InjectRepository(QuickplayEntity)
		private quickplayRepository: Repository<QuickplayEntity>,
		private readonly redisService: RedisService,
		private readonly redisKeyManager: RedisKeyManagerService,
		private readonly sysConfig: SysConfigService,
	) {}

	private buildCacheKey(info: string): string {
		const hash = hashCacheKey(info);
		return this.redisKeyManager.quickplay.limitRate(hash);
	}

	private async checkQuickplayLimit(info: string): Promise<void> {
		const quickplayLimitRateKey = this.buildCacheKey(info);
		const pipeline = this.redisService.createPipeline();
		pipeline.incr(quickplayLimitRateKey);
		pipeline.ttl(quickplayLimitRateKey);

		const results = await pipeline.exec();
		const [infoCount, infoTtl] = results?.map(
			(r: any) => r?.[1] as number,
		) || [0, -1];

		const maxAttempts = await this.sysConfig.getValue<number>('quickplay_max_attempts') || 10;
		const limitTtl = await this.sysConfig.getValue<number>('quickplay_limit_ttl') || 5;

		if (infoTtl === -1) {
			await this.redisService.expire(quickplayLimitRateKey, limitTtl * 60);
		}

		if ((infoCount || 0) > maxAttempts) {
			throw new TooManyRequestsException();
		}
	}

	private async createQuickplay(
		quickplayDto: QuickplayDto,
		ip: string,
	): Promise<QuickplayResponseDto> {
		const quickplayId = `${SocialCode.get(
			UserAccountType.QUICKPLAY,
		)}_${GeneratorProvider.uuidNoHyphens()}`;

		await this.quickplayRepository.save({ ...quickplayDto, quickplayId, ipAddress: ip });

		return new QuickplayResponseDto({
			user: {
				username: quickplayId,
			},
		});
	}

	async loginQuickplay(
		quickplayDto: QuickplayDto,
		ip: string,
	): Promise<QuickplayResponseDto> {
		const info = `${quickplayDto.gameKey}:${quickplayDto.platform}:${quickplayDto.deviceId}:${ip}`;
		await this.checkQuickplayLimit(info);

		const quickplay = await this.quickplayRepository.findOneBy({
			gameKey: quickplayDto.gameKey,
			platform: quickplayDto.platform,
			deviceId: quickplayDto.deviceId,
		});
		if (!quickplay) {
			// Create new quickplay account
			return this.createQuickplay(quickplayDto, ip);
		}
		return new QuickplayResponseDto({
			user: {
				username: quickplay.quickplayId,
			},
		});
	}

	async checkQuickplayExists(quickplayId: string, gameKey: string): Promise<boolean> {
		const quickplay = await this.quickplayRepository.findOneBy({
			quickplayId,
			gameKey,
		});
		return !!quickplay;
	}
}
